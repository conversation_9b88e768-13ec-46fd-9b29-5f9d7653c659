@echo off
echo ========================================
echo Venus Attendance Report Web Application
echo Complete Installation Script
echo ========================================
echo.

echo Installing Python dependencies...
pip install -r requirements_complete.txt

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies!
    echo Please check your Python installation and internet connection.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Project structure:
echo - modules/          : Internal self-contained modules
echo - templates/        : HTML templates
echo - static/          : CSS, JS, and static files
echo - data/            : Local database files
echo - exports/         : Generated reports
echo.
echo To run the application:
echo   python web_app.py
echo.
echo Access the application at: http://localhost:5173
echo.
echo Database Configuration:
echo - Local database: localhost:1433 (VenusHR14)
echo - Remote database: configured in config.json
echo - Auto-fallback enabled between local and remote
echo.
pause 
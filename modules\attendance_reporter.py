"""
Self-contained Attendance Reporter module for VenusHR14 database.
Generates attendance reports based on data from the HR_T_TAMachine_Summary table.
"""

import os
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Optional, Tuple
import calendar
import pyodbc
import json

# Import our internal modules
try:
    from .db_connection import DatabaseConnection
except ImportError:
    from db_connection import DatabaseConnection
try:
    from .export_manager import ExportManager
except ImportError:
    from export_manager import ExportManager

# Configure logging
logger = logging.getLogger(__name__)

class AttendanceReporter:
    """
    Self-contained attendance reporter with built-in database connection management.
    Generates attendance reports based on data from HR_T_TAMachine_Summary.
    """

    def __init__(self, config_file: str = None):
        """
        Initialize the attendance reporter.
        
        Args:
            config_file: Path to configuration file
        """
        try:
            self.db_connection = DatabaseConnection(config_file)
            self.export_manager = ExportManager(
                export_dir=os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports'))
            )
            
            # Create exports directory if it doesn't exist
            os.makedirs(
                os.path.abspath(os.path.join(os.path.dirname(__file__), '../exports')), 
                exist_ok=True
            )
            
            # Load national holidays
            self.national_holidays = self._load_national_holidays()
            
            # Load employee exclusion list
            self._load_employee_exclusion_list()
            
            # Initialize PTRJ Mill database connection
            self._initialize_ptrj_mill_connection()
            
            logger.info("AttendanceReporter initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize AttendanceReporter: {str(e)}")
            raise

    def _load_employee_exclusion_list(self):
        """Load employee exclusion list from JSON file."""
        self.employee_exclusion_list = []
        self.exclusion_settings = {
            "enabled": True,
            "case_sensitive": False,
            "partial_match": True,
            "trim_whitespace": True
        }
        
        try:
            exclusion_file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                'config', 
                'employee_exclusion_list.json'
            )
            
            if os.path.exists(exclusion_file_path):
                with open(exclusion_file_path, 'r', encoding='utf-8') as f:
                    exclusion_data = json.load(f)
                    
                self.employee_exclusion_list = exclusion_data.get('excluded_employees', [])
                self.exclusion_settings.update(exclusion_data.get('exclusion_settings', {}))
                
                logger.info(f"Loaded {len(self.employee_exclusion_list)} employees to exclusion list")
                logger.info(f"Exclusion settings: {self.exclusion_settings}")
            else:
                logger.warning(f"Employee exclusion list file not found: {exclusion_file_path}")
                
        except Exception as e:
            logger.error(f"Error loading employee exclusion list: {str(e)}")
            self.employee_exclusion_list = []

    def _is_employee_excluded(self, employee_name: str) -> bool:
        """Check if employee should be excluded based on exclusion list."""
        if not self.exclusion_settings.get('enabled', True):
            return False
            
        if not self.employee_exclusion_list:
            return False
            
        # Normalize employee name for comparison
        if self.exclusion_settings.get('trim_whitespace', True):
            employee_name = employee_name.strip()
            
        # Case-insensitive comparison
        if not self.exclusion_settings.get('case_sensitive', False):
            employee_name_lower = employee_name.lower()
            
            for excluded_name in self.employee_exclusion_list:
                excluded_name_normalized = excluded_name.strip().lower()
                
                if self.exclusion_settings.get('partial_match', True):
                    if excluded_name_normalized in employee_name_lower or employee_name_lower in excluded_name_normalized:
                        logger.debug(f"Employee '{employee_name}' matched exclusion entry '{excluded_name}' (partial)")
                        return True
                else:
                    if excluded_name_normalized == employee_name_lower:
                        logger.debug(f"Employee '{employee_name}' matched exclusion entry '{excluded_name}' (exact)")
                        return True
        else:
            # Case-sensitive comparison
            for excluded_name in self.employee_exclusion_list:
                excluded_name_normalized = excluded_name.strip()
                
                if self.exclusion_settings.get('partial_match', True):
                    if excluded_name_normalized in employee_name or employee_name in excluded_name_normalized:
                        logger.debug(f"Employee '{employee_name}' matched exclusion entry '{excluded_name}' (partial, case-sensitive)")
                        return True
                else:
                    if excluded_name_normalized == employee_name:
                        logger.debug(f"Employee '{employee_name}' matched exclusion entry '{excluded_name}' (exact, case-sensitive)")
                        return True
                        
        return False

    def _has_employee_activity(self, employee_id: str, attendance_data: List[Dict[str, Any]]) -> bool:
        """Check if employee has any recorded work activity."""
        employee_records = [record for record in attendance_data if record['EmployeeID'] == employee_id]
        
        if not employee_records:
            return False
            
        # Check for any meaningful activity
        for record in employee_records:
            # Check for check-in or check-out
            if record.get('TACheckIn') or record.get('TACheckOut'):
                return True
                
            # Check for recorded hours
            regular_hours = float(record.get('RegularHours', 0))
            overtime_hours = float(record.get('OvertimeHours', 0))
            
            if regular_hours > 0 or overtime_hours > 0:
                return True
                
            # Check for work minutes  
            total_minutes = record.get('TotalMinutesWorked', 0)
            if total_minutes and total_minutes > 0:
                return True
                
        return False

    def _apply_employee_filters(self, employees: List[Dict[str, Any]], attendance_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply both exclusion list and zero-activity filters to employee list."""
        filtered_employees = []
        total_employees = len(employees)
        excluded_by_list = 0
        excluded_by_activity = 0
        
        for employee in employees:
            employee_id = employee['EmployeeID']
            employee_name = employee['EmployeeName']
            
            # Filter 1: Check exclusion list
            if self._is_employee_excluded(employee_name):
                excluded_by_list += 1
                logger.debug(f"Employee '{employee_name}' excluded by exclusion list")
                continue
                
            # Filter 2: Check for zero activity
            if not self._has_employee_activity(employee_id, attendance_data):
                excluded_by_activity += 1
                logger.debug(f"Employee '{employee_name}' excluded due to zero activity")
                continue
                
            # Employee passed both filters
            filtered_employees.append(employee)
        
        logger.info(f"Employee filtering results:")
        logger.info(f"  Total employees: {total_employees}")
        logger.info(f"  Excluded by exclusion list: {excluded_by_list}")
        logger.info(f"  Excluded by zero activity: {excluded_by_activity}")
        logger.info(f"  Remaining employees: {len(filtered_employees)}")
        
        return filtered_employees

    def _initialize_ptrj_mill_connection(self):
        """Initialize PTRJ Mill database connection configuration."""
        self.ptrj_mill_config = None
        self.ptrj_mill_enabled = False
        
        try:
            # Try to load PTRJ Mill database config from existing config
            if hasattr(self.db_connection, 'config'):
                config = self.db_connection.config
                ptrj_config = config.get('database_config', {}).get('ptrj_mill_database', {})
                
                if ptrj_config.get('enabled', False):
                    self.ptrj_mill_config = ptrj_config
                    self.ptrj_mill_enabled = True
                    logger.info("PTRJ Mill database configuration loaded and enabled")
                else:
                    logger.info("PTRJ Mill database disabled in configuration")
            else:
                logger.warning("Database configuration not available for PTRJ Mill setup")
                
        except Exception as e:
            logger.error(f"Error initializing PTRJ Mill connection: {str(e)}")
            self.ptrj_mill_enabled = False

    def _get_ptrj_mill_connection(self):
        """Get connection to PTRJ Mill database."""
        if not self.ptrj_mill_enabled or not self.ptrj_mill_config:
            return None
            
        try:
            import pyodbc
            
            server = self.ptrj_mill_config.get('server', 'localhost')
            port = self.ptrj_mill_config.get('port', 1433)
            database = self.ptrj_mill_config.get('database', 'db_ptrj_mill')
            username = self.ptrj_mill_config.get('username', 'sa')
            password = self.ptrj_mill_config.get('password', '')
            driver = self.ptrj_mill_config.get('driver', 'ODBC Driver 17 for SQL Server')
            timeout = self.ptrj_mill_config.get('timeout', 15)
            
            connection_string = (
                f"DRIVER={{{driver}}};"
                f"SERVER={server},{port};"
                f"DATABASE={database};"
                f"UID={username};"
                f"PWD={password};"
                f"TrustServerCertificate=yes;"
                f"Encrypt=yes;"
                f"Timeout={timeout};"
            )
            
            conn = pyodbc.connect(connection_string)
            logger.debug("PTRJ Mill database connection established")
            return conn
            
        except Exception as e:
            logger.error(f"Error connecting to PTRJ Mill database: {str(e)}")
            return None

    def get_ptrj_employee_mapping(self) -> Dict[str, str]:
        """Get mapping of employee names to PTRJ Employee IDs."""
        ptrj_mapping = {}
        
        if not self.ptrj_mill_enabled:
            logger.warning("PTRJ Mill database not enabled, returning empty mapping")
            return ptrj_mapping
            
        try:
            conn = self._get_ptrj_mill_connection()
            if not conn:
                logger.warning("Could not connect to PTRJ Mill database")
                return ptrj_mapping
                
            cursor = conn.cursor()
            
            # Query PTRJ Mill database for employee mapping
            query = """
            SELECT [EmpCode], [EmpName], [Gender], [MaritalStatus], [Status], [LocCode]
            FROM [db_ptrj_mill].[dbo].[HR_EMPLOYEE]
            WHERE [Status] = '1'
            ORDER BY [EmpName]
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Create mapping with various matching strategies
            for row in results:
                emp_code = row[0] if row[0] else "N/A"
                emp_name = row[1] if row[1] else ""
                
                if emp_name:
                    # Store original name as key
                    ptrj_mapping[emp_name] = emp_code
                    
                    # Store normalized versions for better matching
                    emp_name_lower = emp_name.lower().strip()
                    ptrj_mapping[emp_name_lower] = emp_code
                    
                    # Store name without spaces
                    emp_name_nospace = emp_name.replace(" ", "").lower()
                    ptrj_mapping[emp_name_nospace] = emp_code
            
            conn.close()
            logger.info(f"Retrieved {len(results)} PTRJ employee records")
            
        except Exception as e:
            logger.error(f"Error retrieving PTRJ employee mapping: {str(e)}")
            
        return ptrj_mapping

    def _match_ptrj_employee_id(self, venus_employee_name: str, ptrj_mapping: Dict[str, str]) -> str:
        """Match Venus employee name to PTRJ Employee ID with fuzzy matching."""
        if not venus_employee_name or not ptrj_mapping:
            return "N/A"
            
        venus_name = venus_employee_name.strip()
        
        # Strategy 1: Exact match
        if venus_name in ptrj_mapping:
            return ptrj_mapping[venus_name]
            
        # Strategy 2: Case-insensitive match
        venus_name_lower = venus_name.lower()
        if venus_name_lower in ptrj_mapping:
            return ptrj_mapping[venus_name_lower]
            
        # Strategy 3: No spaces match
        venus_name_nospace = venus_name.replace(" ", "").lower()
        if venus_name_nospace in ptrj_mapping:
            return ptrj_mapping[venus_name_nospace]
            
        # Strategy 4: Partial match - check if Venus name contains PTRJ name or vice versa
        for ptrj_name, ptrj_code in ptrj_mapping.items():
            ptrj_name_clean = ptrj_name.lower().strip()
            
            # Skip already processed entries
            if ptrj_name_clean == ptrj_name:
                # Check partial matches
                if ptrj_name_clean in venus_name_lower or venus_name_lower in ptrj_name_clean:
                    logger.debug(f"Partial match found: '{venus_name}' → '{ptrj_name}' → {ptrj_code}")
                    return ptrj_code
                    
        # Strategy 5: Word-based matching
        venus_words = set(venus_name_lower.split())
        for ptrj_name, ptrj_code in ptrj_mapping.items():
            if ptrj_name == ptrj_name.lower():  # Only process original entries
                ptrj_words = set(ptrj_name.split())
                
                # Check if majority of words match
                if len(venus_words) > 0 and len(ptrj_words) > 0:
                    common_words = venus_words.intersection(ptrj_words)
                    if len(common_words) >= min(len(venus_words), len(ptrj_words)) * 0.6:  # 60% match
                        logger.debug(f"Word-based match found: '{venus_name}' → '{ptrj_name}' → {ptrj_code}")
                        return ptrj_code
        
        # No match found
        logger.debug(f"No PTRJ Employee ID match found for: {venus_name}")
        return "N/A"

    def _load_national_holidays(self):
        """Load national holidays from JSON file."""
        try:
            holidays_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'national_holidays_2025.json')
            with open(holidays_file, 'r', encoding='utf-8') as f:
                holidays_data = json.load(f)
            
            # Create a set of holiday dates for fast lookup
            holiday_dates = set()
            holiday_info = {}
            for holiday in holidays_data['holidays']:
                holiday_dates.add(holiday['date'])
                holiday_info[holiday['date']] = holiday['description']
            
            logger.info(f"Loaded {len(holiday_dates)} national holidays for {holidays_data['year']}")
            return {'dates': holiday_dates, 'info': holiday_info}
        except Exception as e:
            logger.warning(f"Failed to load national holidays: {e}")
            return {'dates': set(), 'info': {}}
    
    def is_national_holiday(self, date_str):
        """Check if a date is a national holiday."""
        return date_str in self.national_holidays['dates']
    
    def get_holiday_info(self, date_str):
        """Get holiday information for a date."""
        return self.national_holidays['info'].get(date_str, None)

    def get_connection(self):
        """Get database connection for enhanced connection management."""
        return self.db_connection

    def get_absence_data(self, start_date: str, end_date: str, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get employee absence data from HR_T_Absence table."""
        try:
            absence_query = """
            SELECT
                abs_table.[BusCode],
                abs_table.[AbsNumber],
                abs_table.[Periode],
                abs_table.[EmployeeID],
                emp_table.[EmployeeName],
                abs_table.[AbsType],
                abs_table.[AbsReason],
                abs_table.[FromDate],
                abs_table.[ToDate],
                abs_table.[Duration],
                abs_table.[Description],
                abs_table.[AppStatus],
                abs_table.[NoticedDate]
            FROM [VenusHR14].[dbo].[HR_T_Absence] AS abs_table
            INNER JOIN [VenusHR14].[dbo].[HR_M_EmployeePI] AS emp_table
            ON abs_table.EmployeeID = emp_table.EmployeeID
            WHERE (abs_table.[FromDate] <= ? AND abs_table.[ToDate] >= ?)
            """
            
            absence_params = [end_date, start_date]
            if bus_code:
                absence_query += " AND abs_table.[BusCode] = ?"
                absence_params.append(bus_code)
            absence_query += " ORDER BY abs_table.EmployeeID, abs_table.[FromDate]"

            results = self.db_connection.execute_query(absence_query, tuple(absence_params))
            logger.info(f"Retrieved {len(results)} absence records from HR_T_Absence")
            
            # Expand date ranges into individual day records
            expanded_absence_data = []
            for record in results:
                expanded_days = self._expand_absence_date_range(record)
                expanded_absence_data.extend(expanded_days)
                
            logger.info(f"Expanded to {len(expanded_absence_data)} individual absence day records")
            return expanded_absence_data

        except Exception as e:
            logger.error(f"Error retrieving absence data: {str(e)}")
            return []

    def _expand_absence_date_range(self, absence_record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Expand absence record from FromDate-ToDate into individual day records."""
        expanded_records = []
        
        try:
            from_date = absence_record['FromDate']
            to_date = absence_record['ToDate']
            
            # Convert to datetime objects if they're strings
            if isinstance(from_date, str):
                from_date = datetime.strptime(from_date, '%Y-%m-%d')
            if isinstance(to_date, str):
                to_date = datetime.strptime(to_date, '%Y-%m-%d')
            
            # Generate individual day records
            current_date = from_date
            while current_date <= to_date:
                day_record = {
                    'EmployeeID': absence_record['EmployeeID'],
                    'EmployeeName': absence_record['EmployeeName'],
                    'AbsDate': current_date.strftime('%Y-%m-%d'),
                    'AbsType': absence_record['AbsType'],
                    'AbsReason': absence_record['AbsReason'],
                    'Description': absence_record['Description'],
                    'AppStatus': absence_record['AppStatus'],
                    'BusCode': absence_record['BusCode'],
                    'AbsNumber': absence_record['AbsNumber'],
                    'Duration': absence_record['Duration'],
                    'FromDate': absence_record['FromDate'],
                    'ToDate': absence_record['ToDate'],
                    # Enhanced fields for grid display
                    'is_unpaid_leave': absence_record['AbsType'].lower() == 'unpaid leave',
                    'display_text': 'ALFA' if absence_record['AbsType'].lower() == 'unpaid leave' else absence_record['AbsType'],
                    'css_class': 'absence-unpaid' if absence_record['AbsType'].lower() == 'unpaid leave' else 'absence-other'
                }
                expanded_records.append(day_record)
                current_date += timedelta(days=1)
                
        except Exception as e:
            logger.error(f"Error expanding absence date range: {e}")
            # Return original record as fallback
            expanded_records.append({
                'EmployeeID': absence_record['EmployeeID'],
                'EmployeeName': absence_record['EmployeeName'],
                'AbsDate': str(absence_record['FromDate']),
                'AbsType': absence_record['AbsType'],
                'AbsReason': absence_record['AbsReason'],
                'Description': absence_record['Description'],
                'AppStatus': absence_record['AppStatus'],
                'BusCode': absence_record['BusCode'],
                'AbsNumber': absence_record['AbsNumber'],
                'Duration': absence_record['Duration'],
                'FromDate': absence_record['FromDate'],
                'ToDate': absence_record['ToDate'],
                'is_unpaid_leave': absence_record['AbsType'].lower() == 'unpaid leave',
                'display_text': 'ALFA' if absence_record['AbsType'].lower() == 'unpaid leave' else absence_record['AbsType'],
                'css_class': 'absence-unpaid' if absence_record['AbsType'].lower() == 'unpaid leave' else 'absence-other'
            })
        
        return expanded_records

    def _calculate_consecutive_working_days(self, start_date: str, duration: int) -> List[str]:
        """
        Calculate consecutive working days starting from start_date.
        Skips weekends (Sunday) and national holidays when counting leave days.
        
        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            duration: Number of working days to count
            
        Returns:
            List of date strings in 'YYYY-MM-DD' format for each leave day
        """
        if duration <= 0:
            return []
        
        try:
            current_date = datetime.strptime(start_date, '%Y-%m-%d')
            leave_dates = []
            working_days_counted = 0
            
            # Safety limit to prevent infinite loops
            max_iterations = duration * 3  # Allow for weekends and holidays
            iterations = 0
            
            while working_days_counted < duration and iterations < max_iterations:
                current_date_str = current_date.strftime('%Y-%m-%d')
                
                # Check if this is a working day (not Sunday or national holiday)
                is_sunday = current_date.weekday() == 6  # Sunday = 6
                is_national_holiday = self.is_national_holiday(current_date_str)
                
                if not is_sunday and not is_national_holiday:
                    # This is a working day, count it
                    leave_dates.append(current_date_str)
                    working_days_counted += 1
                
                # Move to next day
                current_date += timedelta(days=1)
                iterations += 1
            
            logger.info(f"Calculated {len(leave_dates)} consecutive working days from {start_date} (duration: {duration})")
            return leave_dates
            
        except Exception as e:
            logger.error(f"Error calculating consecutive working days: {e}")
            return [start_date]  # Fallback to original date only

    def get_leave_data(self, start_date: str, end_date: str, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get employee leave data from HR_H_Leave table with multi-day leave expansion."""
        try:
            leave_query = """
            SELECT
                leave_table.[Periode],
                leave_table.[EmployeeID],
                employee_table.[EmployeeName],
                leave_table.[LeaveTypeCode],
                leave_table.[LeaveYear],
                leave_table.[RefNumber],
                leave_table.[RefDate],
                leave_table.[BusCode],
                leave_table.[Incoming],
                leave_table.[Outgoing]
            FROM [VenusHR14].[dbo].[HR_H_Leave] AS leave_table
            INNER JOIN [VenusHR14].[dbo].[HR_M_EmployeePI] AS employee_table
            ON leave_table.EmployeeID = employee_table.EmployeeID
            WHERE leave_table.[RefDate] BETWEEN ? AND ?
            """
            leave_params = [start_date, end_date]
            if bus_code:
                leave_query += " AND leave_table.[BusCode] = ?"
                leave_params.append(bus_code)
            leave_query += " ORDER BY leave_table.EmployeeID, leave_table.[RefDate]"

            raw_results = self.db_connection.execute_query(leave_query, tuple(leave_params))
            logger.info(f"Retrieved {len(raw_results)} raw leave records from HR_H_Leave")
            
            # Expand leave records into individual day records
            expanded_leave_data = []
            for leave_record in raw_results:
                expanded_days = self._expand_leave_date_range(leave_record)
                expanded_leave_data.extend(expanded_days)
                
            logger.info(f"Expanded to {len(expanded_leave_data)} individual leave day records")
            return expanded_leave_data

        except Exception as e:
            logger.error(f"Error retrieving leave data: {str(e)}")
            return []

    def _expand_leave_date_range(self, leave_record: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Expand leave record based on RefDate and Outgoing duration into individual day records.
        Uses consecutive working days calculation (skips weekends and holidays).
        """
        expanded_records = []
        
        try:
            ref_date = leave_record['RefDate']
            outgoing_duration = leave_record.get('Outgoing', 1)  # Default to 1 day if not specified
            
            # Convert RefDate to string format
            if isinstance(ref_date, datetime):
                ref_date_str = ref_date.strftime('%Y-%m-%d')
            else:
                ref_date_str = str(ref_date)
            
            # Handle different duration value types
            if outgoing_duration is None:
                duration = 1
            elif isinstance(outgoing_duration, str):
                try:
                    duration = int(float(outgoing_duration))
                except (ValueError, TypeError):
                    duration = 1
            else:
                duration = int(outgoing_duration)
            
            # Ensure minimum duration is 1
            if duration <= 0:
                duration = 1
            
            # Calculate consecutive working days
            leave_dates = self._calculate_consecutive_working_days(ref_date_str, duration)
            
            # Create individual day records for each leave date
            for leave_date in leave_dates:
                day_record = {
                    'EmployeeID': leave_record['EmployeeID'],
                    'EmployeeName': leave_record['EmployeeName'],
                    'LeaveTypeCode': leave_record['LeaveTypeCode'],
                    'LeaveYear': leave_record['LeaveYear'],
                    'RefNumber': leave_record['RefNumber'],
                    'RefDate': leave_date,  # Individual leave date
                    'BusCode': leave_record['BusCode'],
                    'Incoming': leave_record['Incoming'],
                    'Outgoing': leave_record['Outgoing'],
                    'Periode': leave_record['Periode'],
                    # Enhanced fields for tracking
                    'OriginalRefDate': ref_date_str,  # Original RefDate from database
                    'LeaveDuration': duration,        # Total duration in working days
                    'IsExpanded': True,               # Flag to indicate this is an expanded record
                    'source': 'HR_H_Leave_Expanded'
                }
                expanded_records.append(day_record)
                
        except Exception as e:
            logger.error(f"Error expanding leave date range: {e}")
            # Return original record as fallback
            fallback_record = leave_record.copy()
            fallback_record['IsExpanded'] = False
            fallback_record['source'] = 'HR_H_Leave_Original'
            expanded_records.append(fallback_record)
        
        return expanded_records

    def get_leave_type_description(self, leave_type_code: str) -> str:
        """Map leave type codes to their descriptions."""
        leave_type_mapping = {
            'CT': 'CUTI',
            'H2': 'HAMIL/MELAHIRKAN',
            'P1': 'KELUARGA MENINGGAL',
            'P2': 'IZIN MENIKAHKAN ATAU KHITANAN',
            'P3': 'CUTI MENIKAH',
            'CB': 'CUTI BERSAMA',
            'S': 'SAKIT',
            'I': 'IZIN'
        }
        return leave_type_mapping.get(leave_type_code, leave_type_code)

    def _get_display_text_for_grid(self, final_status: str, absence_data: Dict, leave_data: Dict, normal_hours: float, overtime_hours: float) -> str:
        """Get display text for grid cells based on status and data."""
        if final_status == "absence_unpaid_alfa":
            return "ALFA"  # Unpaid leave displays as ALFA
        elif final_status == "absence_other" and absence_data:
            return absence_data['AbsType']  # Other absence types show actual type
        elif final_status == "on_leave" and leave_data:
            return leave_data['LeaveTypeCode']  # Leave type codes (CT, H2, P1, etc.)
        elif final_status == "alfa":
            return "ALFA"  # No attendance and no leave/absence on working day
        elif final_status == "off":
            return "OFF"  # Weekend/holiday with no overtime
        elif final_status == "overtime_only":
            return f"(0) | ({overtime_hours:.1f})"  # Overtime only format
        elif final_status in ["partial_check_in_only", "partial_check_out_only"]:
            return f"({normal_hours:.1f}) | (0)"  # Partial attendance gets default hours
        elif final_status == "complete":
            if overtime_hours > 0:
                return f"({normal_hours:.1f}) | ({overtime_hours:.1f})"  # Normal + overtime
            else:
                return f"({normal_hours:.1f}) | (0)"  # Normal hours only
        else:
            return "-"  # Default fallback

    def _get_css_class_for_grid(self, final_status: str, absence_data: Dict) -> str:
        """Get CSS class for grid cell styling."""
        if final_status == "absence_unpaid_alfa":
            return "absence-unpaid-alfa"  # Red background for unpaid leave (ALFA)
        elif final_status == "absence_other":
            return "absence-other"  # Styling for other absence types
        elif final_status == "on_leave":
            return "on-leave"  # Styling for HR_H_Leave data
        elif final_status == "alfa":
            return "alfa-absent"  # Red background for ALFA (no leave/absence)
        elif final_status == "off":
            return "day-off"  # Weekend/holiday styling
        elif final_status == "overtime_only":
            return "overtime-only"  # Overtime only styling
        elif final_status in ["partial_check_in_only", "partial_check_out_only"]:
            return "partial-attendance"  # Blue background for partial attendance
        elif final_status == "complete":
            return "complete-attendance"  # Normal attendance styling
        else:
            return "default-cell"  # Default styling

    def get_attendance_data(self, start_date: str, end_date: str, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get attendance data from both attendance and overtime tables with leave integration."""
        try:
            # Query 1: Get normal attendance data from HR_T_TAMachine_Summary
            attendance_query = """
            SELECT
                t.BusCode,
                t.UserDeviceID,
                t.UserDeviceName,
                t.EmployeeID,
                t.TADate,
                t.TACheckIn,
                t.TACheckOut,
                t.Shift,
                t.IsCrossDay,
                emp.EmployeeName,
                emp.Gender
            FROM HR_T_TAMachine_Summary t
            LEFT JOIN HR_M_EmployeePI emp ON t.EmployeeID = emp.EmployeeID
            WHERE t.TADate BETWEEN ? AND ?
            """
            attendance_params = [start_date, end_date]
            if bus_code:
                attendance_query += " AND t.BusCode = ?"
                attendance_params.append(bus_code)
            attendance_query += " ORDER BY t.EmployeeID, t.TADate"
            
            # Query 2: Get overtime data from HR_T_Overtime
            overtime_query = """
            SELECT 
                ot.BusCode,
                ot.EmployeeID,
                ot.OTDate,
                ot.OTHourDuration,
                ot.OTStart,
                ot.OTFinish,
                ot.Description,
                ot.AppStatus
            FROM HR_T_Overtime ot
            WHERE ot.OTDate BETWEEN ? AND ?
            AND ot.OTHourDuration > 0
            """
            overtime_params = [start_date, end_date]
            if bus_code:
                overtime_query += " AND ot.BusCode = ?"
                overtime_params.append(bus_code)
            overtime_query += " ORDER BY ot.EmployeeID, ot.OTDate"
            
            # Execute all queries
            attendance_results = self.db_connection.execute_query(attendance_query, tuple(attendance_params))
            overtime_results = self.db_connection.execute_query(overtime_query, tuple(overtime_params))
            leave_results = self.get_leave_data(start_date, end_date, bus_code)
            absence_results = self.get_absence_data(start_date, end_date, bus_code)

            # Create overtime lookup dictionary
            overtime_lookup = {}
            for ot_record in overtime_results:
                employee_id = ot_record['EmployeeID']
                ot_date = ot_record['OTDate']
                if isinstance(ot_date, datetime):
                    ot_date_str = ot_date.strftime('%Y-%m-%d')
                else:
                    ot_date_str = str(ot_date)

                key = f"{employee_id}_{ot_date_str}"
                if key not in overtime_lookup:
                    overtime_lookup[key] = 0

                # Sum up overtime hours for the same employee and date
                ot_hours = float(ot_record.get('OTHourDuration', 0) or 0)
                overtime_lookup[key] += ot_hours

            # Create leave lookup dictionary (HR_H_Leave) - Now with expanded multi-day leaves
            leave_lookup = {}
            for leave_record in leave_results:
                employee_id = leave_record['EmployeeID']
                leave_date = leave_record['RefDate']  # This is now individual leave date from expansion
                if isinstance(leave_date, datetime):
                    leave_date_str = leave_date.strftime('%Y-%m-%d')
                else:
                    leave_date_str = str(leave_date)

                key = f"{employee_id}_{leave_date_str}"
                leave_lookup[key] = {
                    'LeaveTypeCode': leave_record['LeaveTypeCode'],
                    'LeaveTypeDescription': self.get_leave_type_description(leave_record['LeaveTypeCode']),
                    'RefNumber': leave_record['RefNumber'],
                    'Periode': leave_record['Periode'],
                    'source': leave_record.get('source', 'HR_H_Leave'),
                    # Enhanced fields from expansion
                    'OriginalRefDate': leave_record.get('OriginalRefDate'),
                    'LeaveDuration': leave_record.get('LeaveDuration'),
                    'IsExpanded': leave_record.get('IsExpanded', False),
                    'Incoming': leave_record.get('Incoming'),
                    'Outgoing': leave_record.get('Outgoing')
                }

            # Create absence lookup dictionary (HR_T_Absence) - HIGHER PRIORITY
            absence_lookup = {}
            for absence_record in absence_results:
                employee_id = absence_record['EmployeeID']
                abs_date = absence_record['AbsDate']  # Already formatted as YYYY-MM-DD
                
                key = f"{employee_id}_{abs_date}"
                absence_lookup[key] = {
                    'AbsType': absence_record['AbsType'],
                    'AbsReason': absence_record['AbsReason'],
                    'Description': absence_record['Description'],
                    'AbsNumber': absence_record['AbsNumber'],
                    'AppStatus': absence_record['AppStatus'],
                    'is_unpaid_leave': absence_record['is_unpaid_leave'],
                    'display_text': absence_record['display_text'],
                    'css_class': absence_record['css_class'],
                    'source': 'HR_T_Absence',
                    'Duration': absence_record['Duration'],
                    'FromDate': absence_record['FromDate'],
                    'ToDate': absence_record['ToDate']
                }
            
            # Process attendance data and combine with overtime
            combined_results = []
            for record in attendance_results:
                employee_id = record['EmployeeID']
                ta_date = record['TADate']
                
                if isinstance(ta_date, datetime):
                    ta_date_str = ta_date.strftime('%Y-%m-%d')
                    weekday = ta_date.weekday()  # 0=Monday, 6=Sunday
                else:
                    ta_date_obj = datetime.strptime(str(ta_date), '%Y-%m-%d')
                    ta_date_str = str(ta_date)
                    weekday = ta_date_obj.weekday()
                
                # Calculate normal working hours from check-in/check-out
                check_in = record.get('TACheckIn')
                check_out = record.get('TACheckOut')
                normal_hours = 0
                total_minutes = 0
                status = "absent"
                
                # Check attendance completeness
                has_check_in = check_in is not None and str(check_in).strip() != ''
                has_check_out = check_out is not None and str(check_out).strip() != ''
                
                if has_check_in and has_check_out:
                    # Complete attendance - calculate actual hours
                    try:
                        if isinstance(check_in, str) and check_in.strip():
                            # Handle string time formats like "07:33" or "07:33:44"
                            time_parts = check_in.split(':')
                            if len(time_parts) >= 2:
                                check_in_time = datetime.strptime(f"{time_parts[0]}:{time_parts[1]}", '%H:%M').time()
                            else:
                                check_in_time = datetime.strptime(check_in, '%H:%M').time()
                        elif hasattr(check_in, 'time'):
                            # datetime object
                            check_in_time = check_in.time()
                        else:
                            # time object
                            check_in_time = check_in
                            
                        if isinstance(check_out, str) and check_out.strip():
                            # Handle string time formats like "16:06" or "16:06:22"
                            time_parts = check_out.split(':')
                            if len(time_parts) >= 2:
                                check_out_time = datetime.strptime(f"{time_parts[0]}:{time_parts[1]}", '%H:%M').time()
                            else:
                                check_out_time = datetime.strptime(check_out, '%H:%M').time()
                        elif hasattr(check_out, 'time'):
                            # datetime object
                            check_out_time = check_out.time()
                        else:
                            # time object
                            check_out_time = check_out
                        
                        # Convert to datetime for calculation
                        base_date = ta_date.date() if isinstance(ta_date, datetime) else datetime.strptime(ta_date_str, '%Y-%m-%d').date()
                        check_in_dt = datetime.combine(base_date, check_in_time)
                        check_out_dt = datetime.combine(base_date, check_out_time)
                        
                        # Handle cross-day scenarios
                        if check_out_dt < check_in_dt:
                            check_out_dt += timedelta(days=1)
                        
                        # Calculate total minutes worked
                        total_minutes = (check_out_dt - check_in_dt).total_seconds() / 60
                        total_hours = total_minutes / 60
                        
                        # Apply business rules for normal hours
                        if weekday == 6 or self.is_national_holiday(ta_date_str):  # Sunday or National Holiday
                            normal_hours = 0  # All Sunday and national holiday work is overtime
                        elif weekday == 5:  # Saturday
                            normal_hours = min(total_hours, 5.0)  # Max 5 hours normal on Saturday
                        else:  # Monday-Friday (non-holiday)
                            normal_hours = min(total_hours, 7.0)  # Max 7 hours normal on weekdays
                        
                        status = "complete"
                            
                    except Exception as time_error:
                        logger.warning(f"Time parsing error for {employee_id} on {ta_date_str}: {time_error}")
                        normal_hours = 0
                        total_minutes = 0
                        status = "absent"
                        
                elif has_check_in or has_check_out:
                    # Incomplete attendance - employee forgot to check in or out
                    # Assign default hours: 7 hours for weekdays, 5 for Saturday, 0 for Sunday/National Holiday
                    if weekday == 6 or self.is_national_holiday(ta_date_str):  # Sunday or National Holiday
                        normal_hours = 0
                    elif weekday == 5:  # Saturday  
                        normal_hours = 5.0
                    else:  # Monday-Friday (non-holiday)
                        normal_hours = 7.0
                    
                    # Differentiate between check-in only and check-out only for better visual indication
                    if has_check_in and not has_check_out:
                        status = "partial_check_in_only"  # Blue background
                    elif has_check_out and not has_check_in:
                        status = "partial_check_out_only"  # Blue background
                    else:
                        status = "incomplete_needs_verification"  # Fallback
                        
                    total_minutes = normal_hours * 60  # Estimate minutes
                else:
                    # No attendance data at all
                    normal_hours = 0
                    total_minutes = 0
                    status = "absent"
                
                # Get overtime hours from overtime table
                overtime_key = f"{employee_id}_{ta_date_str}"
                overtime_hours = overtime_lookup.get(overtime_key, 0)

                # Get absence data (HR_T_Absence) and leave data (HR_H_Leave)
                absence_key = f"{employee_id}_{ta_date_str}"
                leave_key = f"{employee_id}_{ta_date_str}"
                absence_data = absence_lookup.get(absence_key, None)
                leave_data = leave_lookup.get(leave_key, None)

                # Determine final status with PRIORITY LOGIC:
                # Priority 1: HR_T_Absence data (if exists)
                # Priority 2: HR_H_Leave data (if exists) 
                # Priority 3: ALFA (absent with no leave/absence on working day)
                # Priority 4: Normal attendance data
                final_status = status
                absence_display_data = None
                leave_display_data = None

                if absence_data:
                    # PRIORITY 1: HR_T_Absence data takes highest priority
                    if absence_data['is_unpaid_leave']:
                        final_status = "absence_unpaid_alfa"  # "Unpaid Leave" → display as "ALFA" with red background
                    else:
                        final_status = "absence_other"  # Other absence types (Sick, Duty, etc.)
                    absence_display_data = absence_data
                    
                elif leave_data:
                    # PRIORITY 2: HR_H_Leave data (secondary priority)
                    final_status = "on_leave"
                    leave_display_data = leave_data
                    
                elif status == "absent":
                    # PRIORITY 3: ALFA logic for absent employees without leave/absence
                    if not (weekday == 6 or self.is_national_holiday(ta_date_str)):
                        # No absence/leave data and absent on working day = ALFA
                        final_status = "alfa"
                    else:
                        # Sunday or national holiday - check for overtime
                        if overtime_hours > 0:
                            final_status = "overtime_only"
                        else:
                            final_status = "off"
                            
                elif weekday == 6 or self.is_national_holiday(ta_date_str):
                    # Weekend/holiday logic even if attendance exists
                    if overtime_hours > 0:
                        final_status = "overtime_only"
                    else:
                        final_status = "off"
                        
                elif status in ["partial_check_in_only", "partial_check_out_only"]:
                    # Keep partial attendance statuses as they are
                    final_status = status

                # Create enhanced record with absence integration
                enhanced_record = {
                    'BusCode': record.get('BusCode'),
                    'UserDeviceID': record.get('UserDeviceID'),
                    'UserDeviceName': record.get('UserDeviceName'),
                    'EmployeeID': employee_id,
                    'EmployeeName': record.get('EmployeeName', ''),
                    'Gender': record.get('Gender', ''),
                    'TADate': ta_date,
                    'TACheckIn': check_in,
                    'TACheckOut': check_out,
                    'Shift': record.get('Shift'),
                    'IsCrossDay': record.get('IsCrossDay', False),
                    'TotalMinutesWorked': total_minutes,
                    'RegularHours': normal_hours,
                    'OvertimeHours': overtime_hours,
                    'TotalHours': normal_hours + overtime_hours,
                    'Status': final_status,
                    'LeaveData': leave_display_data,  # HR_H_Leave data
                    'AbsenceData': absence_display_data,  # HR_T_Absence data (higher priority)
                    # Grid display helpers
                    'DisplayText': self._get_display_text_for_grid(final_status, absence_display_data, leave_display_data, normal_hours, overtime_hours),
                    'CssClass': self._get_css_class_for_grid(final_status, absence_display_data),
                    'IsUnpaidLeave': absence_display_data['is_unpaid_leave'] if absence_display_data else False
                }
                
                combined_results.append(enhanced_record)
            
            logger.info(f"Retrieved {len(combined_results)} attendance records with overtime data")
            return combined_results
            
        except Exception as e:
            logger.error(f"Error retrieving attendance data: {str(e)}")
            return []

    def generate_daily_report(self, date: str, bus_code: str = None) -> str:
        """Generate a daily attendance report."""
        data = self.get_attendance_data(date, date, bus_code)
        if not data:
            return None
        filename = f"daily_report_{date.replace('-', '_')}.xlsx"
        return self.export_manager.export_to_excel(
            data=data, filename=filename, title=f"Daily Attendance Report - {date}"
        )

    def generate_date_range_report(self, start_date: str, end_date: str, bus_code: str = None) -> str:
        """Generate attendance report for a date range."""
        data = self.get_attendance_data(start_date, end_date, bus_code)
        if not data:
            return None
        filename = f"range_report_{start_date.replace('-', '_')}_to_{end_date.replace('-', '_')}.xlsx"
        return self.export_manager.export_to_excel(
            data=data, filename=filename, title=f"Attendance Report - {start_date} to {end_date}"
        )

    def get_employees_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of employees from the database."""
        query = """
        SELECT DISTINCT
            emp.EmployeeID,
            emp.EmployeeName,
            emp.BusCode,
            emp.Gender
        FROM
            HR_M_EmployeePI emp
        WHERE
            emp.EmployeeID IS NOT NULL
            AND emp.EmployeeName IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND emp.BusCode = ?"
            params.append(bus_code)
        query += " ORDER BY emp.EmployeeName"

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            logger.info(f"Retrieved {len(results)} employees")
            return results
        except Exception as e:
            logger.error(f"Error retrieving employees list: {str(e)}")
            return []

    def get_shifts_list(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of shifts from the database."""
        query = """
        SELECT DISTINCT
            t.Shift,
            COUNT(*) as EmployeeCount
        FROM
            HR_T_TAMachine_Summary t
        WHERE
            t.Shift IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)
        query += " GROUP BY t.Shift ORDER BY t.Shift"

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            return results
        except Exception as e:
            logger.error(f"Error retrieving shifts list: {str(e)}")
            return []

    def get_available_months(self, bus_code: str = None) -> List[Dict[str, Any]]:
        """Get list of available months with attendance data."""
        query = """
        SELECT 
            YEAR(t.TADate) as Year,
            MONTH(t.TADate) as Month,
            DATENAME(MONTH, t.TADate) as MonthName,
            COUNT(*) as RecordCount,
            COUNT(DISTINCT t.EmployeeID) as EmployeeCount,
            MIN(t.TADate) as FirstDate,
            MAX(t.TADate) as LastDate
        FROM 
            HR_T_TAMachine_Summary t
        WHERE 
            t.TADate IS NOT NULL
        """
        params = []
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)
        query += """
        GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
        ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
        """

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            return results
        except Exception as e:
            logger.error(f"Error retrieving available months: {str(e)}")
            return []

    def get_monthly_summary(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """Get monthly attendance summary statistics."""
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1
        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        query = """
        SELECT 
            COUNT(*) as TotalRecords,
            COUNT(DISTINCT t.EmployeeID) as TotalEmployees,
            AVG(CASE 
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL 
                THEN DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) / 60.0 
                ELSE 0 
            END) as AvgHoursPerDay,
            SUM(CASE 
                WHEN t.TACheckIn IS NOT NULL AND t.TACheckOut IS NOT NULL 
                THEN DATEDIFF(MINUTE, t.TACheckIn, t.TACheckOut) / 60.0 
                ELSE 0 
            END) as TotalHours
        FROM 
            HR_T_TAMachine_Summary t
        WHERE 
            t.TADate BETWEEN ? AND ?
        """
        params = [start_date, end_date]
        if bus_code:
            query += " AND t.BusCode = ?"
            params.append(bus_code)

        try:
            results = self.db_connection.execute_query(query, tuple(params))
            if results:
                summary = results[0]
                summary.update({
                    'Year': year, 'Month': month, 'MonthName': calendar.month_name[month],
                    'StartDate': start_date, 'EndDate': end_date
                })
                return summary
            return {}
        except Exception as e:
            logger.error(f"Error generating monthly summary: {str(e)}")
            return {}



    def get_monthly_attendance_grid(self, year: int, month: int, bus_code: str = None) -> Dict[str, Any]:
        """Generate enhanced monthly attendance grid with employee filtering and data availability validation."""
        start_date = f"{year}-{month:02d}-01"
        days_in_month = calendar.monthrange(year, month)[1]
        end_date = f"{year}-{month:02d}-{days_in_month:02d}"

        # Get data availability information
        available_dates, latest_available_date, total_days_in_month = self.get_available_dates_in_month(year, month)
        
        # Get attendance data and employee list
        data = self.get_attendance_data(start_date, end_date, bus_code)
        all_employees = self.get_employees_list(bus_code)
        
        # Apply employee filtering (exclusion list + zero activity)
        employees = self._apply_employee_filters(all_employees, data)
        
        # Get PTRJ Employee ID mapping
        ptrj_mapping = self.get_ptrj_employee_mapping()

        # Get leave data (HR_H_Leave) for the month and create lookup - Now with multi-day expansion
        leave_results = self.get_leave_data(start_date, end_date, bus_code)
        leave_lookup = {}
        for leave_record in leave_results:
            employee_id = leave_record['EmployeeID']
            ref_date = leave_record['RefDate']  # This is now individual leave date from expansion
            if isinstance(ref_date, datetime):
                ref_date_str = ref_date.strftime('%Y-%m-%d')
            else:
                ref_date_str = str(ref_date)

            key = f"{employee_id}_{ref_date_str}"
            leave_lookup[key] = {
                'leave_type_code': leave_record['LeaveTypeCode'],
                'leave_type_description': self.get_leave_type_description(leave_record['LeaveTypeCode']),
                'ref_number': leave_record.get('RefNumber', ''),
                'incoming': leave_record.get('Incoming', ''),
                'outgoing': leave_record.get('Outgoing', ''),
                # Enhanced fields from multi-day expansion
                'original_ref_date': leave_record.get('OriginalRefDate'),
                'leave_duration': leave_record.get('LeaveDuration'),
                'is_expanded': leave_record.get('IsExpanded', False),
                'source': leave_record.get('source', 'HR_H_Leave')
            }

        # Get absence data (HR_T_Absence) for the month and create lookup - HIGHER PRIORITY
        absence_results = self.get_absence_data(start_date, end_date, bus_code)
        absence_lookup = {}
        for absence_record in absence_results:
            employee_id = absence_record['EmployeeID']
            abs_date = absence_record['AbsDate']  # Already formatted as YYYY-MM-DD
            
            key = f"{employee_id}_{abs_date}"
            absence_lookup[key] = {
                'AbsType': absence_record['AbsType'],
                'AbsReason': absence_record['AbsReason'],
                'Description': absence_record['Description'],
                'AbsNumber': absence_record['AbsNumber'],
                'AppStatus': absence_record['AppStatus'],
                'is_unpaid_leave': absence_record['is_unpaid_leave'],
                'display_text': absence_record['display_text'],
                'css_class': absence_record['css_class'],
                'source': 'HR_T_Absence',
                'Duration': absence_record['Duration'],
                'FromDate': absence_record['FromDate'],
                'ToDate': absence_record['ToDate']
            }

        grid_data = []
        employee_no = 1

        for employee in employees:
            employee_id = employee['EmployeeID']
            employee_name = employee['EmployeeName']
            employee_records = [record for record in data if record['EmployeeID'] == employee_id]

            days = {}
            for day in range(1, days_in_month + 1):
                day_date = f"{year}-{month:02d}-{day:02d}"
                day_record = next((r for r in employee_records if r['TADate'].strftime('%Y-%m-%d') == day_date), None)
                
                date_obj = datetime(year, month, day)
                is_sunday = date_obj.weekday() == 6  # Sunday
                is_saturday = date_obj.weekday() == 5  # Saturday
                is_national_holiday = self.is_national_holiday(day_date)
                holiday_info = self.get_holiday_info(day_date) if is_national_holiday else None
                
                # Check if data is available for this date
                is_data_available = self.is_date_data_available(date_obj.date(), latest_available_date)
                
                # CRITICAL: If data is not available, mark as data_unavailable with highest priority
                if not is_data_available:
                    days[str(day)] = {
                        'status': 'data_unavailable',
                        'is_data_unavailable': True,
                        'normal_hours': 0,
                        'overtime_hours': 0,
                        'check_in': None,
                        'check_out': None,
                        'is_sunday': is_sunday,
                        'is_saturday': is_saturday,
                        'is_national_holiday': is_national_holiday,
                        'holiday_info': holiday_info,
                        'day_of_week': date_obj.strftime('%A'),
                        'date': day_date
                    }
                    continue  # Skip further processing for unavailable dates
                
                if day_record:
                    # Get data from the combined record
                    normal_hours = float(day_record.get('RegularHours', 0))
                    overtime_hours = float(day_record.get('OvertimeHours', 0))
                    record_status = day_record.get('Status', 'unknown')
                    check_in = day_record.get('TACheckIn')
                    check_out = day_record.get('TACheckOut')
                    total_minutes = day_record.get('TotalMinutesWorked', 0)
                    leave_data = day_record.get('LeaveData')
                    absence_data = day_record.get('AbsenceData')

                    # Determine final status for display with PRIORITY LOGIC:
                    # Priority 1: HR_T_Absence data (if exists)
                    # Priority 2: HR_H_Leave data (if exists) 
                    # Priority 3: Normal attendance status
                    if absence_data:
                        # PRIORITY 1: HR_T_Absence data takes highest priority
                        if absence_data['is_unpaid_leave']:
                            status = "absence_unpaid_alfa"  # "Unpaid Leave" → display as "ALFA" with red background
                        else:
                            status = "absence_other"  # Other absence types (Sick, Duty, etc.)
                    elif leave_data:
                        # PRIORITY 2: HR_H_Leave data (secondary priority)
                        status = "on_leave"
                    elif record_status == "alfa":
                        status = "alfa"  # Red color - absent without leave/absence
                    elif record_status == "overtime_only":
                        status = "overtime_only"  # Sunday/holiday with overtime
                    elif record_status == "off":
                        status = "off"  # Sunday/holiday without overtime
                    elif record_status == "partial_check_in_only":
                        status = "partial_check_in_only"  # Blue background - check in only
                    elif record_status == "partial_check_out_only":
                        status = "partial_check_out_only"  # Blue background - check out only
                    elif record_status == "incomplete_needs_verification":
                        status = "needs_verification"  # Pink color - needs crosscheck
                    elif record_status == "complete":
                        status = "complete"  # Normal colors based on hours
                    else:
                        status = "absent"
                    
                    # Format the day cell data with enhanced absence and leave integration
                    days[str(day)] = {
                        'normal_hours': round(normal_hours, 1),
                        'overtime_hours': round(overtime_hours, 1),
                        'status': status,
                        'is_weekend': is_saturday or is_sunday,
                        'is_sunday': is_sunday,
                        'is_saturday': is_saturday,
                        'is_national_holiday': is_national_holiday,
                        'holiday_info': holiday_info,
                        'has_overtime': overtime_hours > 0,
                        'needs_verification': status == "needs_verification",
                        'is_alfa': status in ["alfa", "absence_unpaid_alfa"],
                        'is_on_leave': status == "on_leave",
                        'is_absence': status in ["absence_unpaid_alfa", "absence_other"],
                        'absence_data': absence_data,  # HR_T_Absence data (highest priority)
                        'leave_data': leave_data,      # HR_H_Leave data (secondary priority)
                        'display_format': 'enhanced_with_absence_and_leave',
                        'raw_data': {
                            'check_in': str(check_in) if check_in else None,
                            'check_out': str(check_out) if check_out else None,
                            'total_minutes': total_minutes,
                            'original_status': record_status
                        }
                    }
                else:
                    # No attendance record for this day - check for absence and leave data
                    date_obj = datetime(year, month, day)
                    ta_date_str = date_obj.strftime('%Y-%m-%d')
                    absence_key = f"{employee_id}_{ta_date_str}"
                    leave_key = f"{employee_id}_{ta_date_str}"
                    absence_data = absence_lookup.get(absence_key, None)
                    leave_data = leave_lookup.get(leave_key, None)

                    # Determine status for no-attendance days with PRIORITY LOGIC
                    if absence_data:
                        # PRIORITY 1: HR_T_Absence data
                        if absence_data['is_unpaid_leave']:
                            final_status = "absence_unpaid_alfa"
                        else:
                            final_status = "absence_other"
                    elif leave_data:
                        # PRIORITY 2: HR_H_Leave data
                        final_status = "on_leave"
                    elif is_sunday or is_national_holiday:
                        final_status = "off"
                    else:
                        final_status = "alfa"  # Absent without absence/leave on working day

                    if is_sunday or is_national_holiday:
                        days[str(day)] = {
                            'normal_hours': 0,
                            'overtime_hours': 0,
                            'status': final_status,
                            'is_weekend': is_sunday or is_saturday,
                            'is_sunday': is_sunday,
                            'is_saturday': is_saturday,
                            'is_national_holiday': is_national_holiday,
                            'holiday_info': holiday_info,
                            'has_overtime': False,
                            'needs_verification': False,
                            'is_alfa': final_status in ["alfa", "absence_unpaid_alfa"],
                            'is_on_leave': final_status == "on_leave",
                            'is_absence': final_status in ["absence_unpaid_alfa", "absence_other"],
                            'absence_data': absence_data,  # HR_T_Absence data (highest priority)
                            'leave_data': leave_data,      # HR_H_Leave data (secondary priority)
                            'display_format': 'enhanced_with_absence_and_leave',
                            'raw_data': None
                        }
                    else:
                        days[str(day)] = {
                            'normal_hours': 0,
                            'overtime_hours': 0,
                            'status': final_status,
                            'is_weekend': is_saturday,
                            'is_sunday': False,
                            'is_saturday': is_saturday,
                            'is_national_holiday': is_national_holiday,
                            'holiday_info': holiday_info,
                            'has_overtime': False,
                            'needs_verification': False,
                            'is_alfa': final_status in ["alfa", "absence_unpaid_alfa"],
                            'is_on_leave': final_status == "on_leave",
                            'is_absence': final_status in ["absence_unpaid_alfa", "absence_other"],
                            'absence_data': absence_data,  # HR_T_Absence data (highest priority)
                            'leave_data': leave_data,      # HR_H_Leave data (secondary priority)
                            'display_format': 'enhanced_with_absence_and_leave',
                            'raw_data': None
                        }

            # Get PTRJ Employee ID for this employee
            ptrj_employee_id = self._match_ptrj_employee_id(employee_name, ptrj_mapping)

            grid_data.append({
                'No': employee_no,
                'EmployeeID': employee_id,
                'PTRJEmployeeID': ptrj_employee_id,
                'EmployeeName': employee_name,
                'days': days
            })
            employee_no += 1

        result = {
            'year': year,
            'month': month,
            'month_name': calendar.month_name[month],
            'days_in_month': days_in_month,
            'total_employees': len(grid_data),
            'grid_data': grid_data,
            'date_range': f"{start_date} to {end_date}",
            'display_format': 'enhanced_with_absence_and_leave',  # Flag to indicate new format with absence integration
            'absence_summary': {
                'total_absence_records': len(absence_results),
                'total_leave_records': len(leave_results),
                'absence_types': list(set([abs_rec['AbsType'] for abs_rec in absence_results])),
                'unpaid_leave_count': len([abs_rec for abs_rec in absence_results if abs_rec['is_unpaid_leave']])
            },
            'filtering_summary': {
                'total_employees_before_filtering': len(all_employees),
                'total_employees_after_filtering': len(employees),
                'employees_filtered_out': len(all_employees) - len(employees),
                'exclusion_list_enabled': self.exclusion_settings.get('enabled', False),
                'exclusion_list_count': len(self.employee_exclusion_list),
                'filtering_applied': True
            },
            'ptrj_mapping_summary': {
                'ptrj_mill_enabled': self.ptrj_mill_enabled,
                'total_ptrj_employees': len(ptrj_mapping) // 3 if ptrj_mapping else 0,  # Divide by 3 because we store 3 variants per employee
                'employees_with_ptrj_id': len([emp for emp in grid_data if emp.get('PTRJEmployeeID', 'N/A') != 'N/A']),
                'employees_without_ptrj_id': len([emp for emp in grid_data if emp.get('PTRJEmployeeID', 'N/A') == 'N/A']),
                'mapping_success_rate': round((len([emp for emp in grid_data if emp.get('PTRJEmployeeID', 'N/A') != 'N/A']) / len(grid_data) * 100), 1) if grid_data else 0
            },
            'data_availability': {
                'latest_available_date': latest_available_date.strftime('%Y-%m-%d') if latest_available_date else None,
                'total_days_in_month': total_days_in_month,
                'available_days_count': len(available_dates),
                'unavailable_days_count': total_days_in_month - len(available_dates),
                'available_dates': [d.strftime('%Y-%m-%d') for d in available_dates],
                'data_coverage_percentage': round((len(available_dates) / total_days_in_month * 100), 1) if total_days_in_month > 0 else 0,
                'has_unavailable_dates': len(available_dates) < total_days_in_month
            }
        }

        return result 

    def get_latest_available_data_date(self):
        """
        Get the latest available data date from HR_T_TAMachine_Summary table
        Returns the latest TADate available in the database
        """
        try:
            # Ensure connection is established
            if not self.db_connection.connection or not hasattr(self.db_connection.connection, 'cursor'):
                self.db_connection.connect()
            
            if not self.db_connection.connection:
                logger.error("❌ Could not establish database connection for latest data date check")
                return None
                
            conn = self.db_connection.connection
            if not conn:
                logger.error("❌ Could not establish database connection for latest data date check")
                return None
                
            cursor = conn.cursor()
            
            # Query to get the latest available data date as specified in requirements
            query = """
            SELECT TOP (1) [TADate] 
            FROM [VenusHR14].[dbo].[HR_T_TAMachine_Summary] 
            ORDER BY TADate DESC
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result and result[0]:
                latest_date = result[0]
                if isinstance(latest_date, str):
                    # Parse string date to datetime
                    latest_date = datetime.strptime(latest_date, '%Y-%m-%d').date()
                elif hasattr(latest_date, 'date'):
                    # Convert datetime to date
                    latest_date = latest_date.date()
                
                logger.info(f"✅ Latest available data date from HR_T_TAMachine_Summary: {latest_date}")
                return latest_date
            else:
                logger.warning("⚠️ No data found in HR_T_TAMachine_Summary table")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting latest available data date: {str(e)}")
            return None
        finally:
            # Ensure connection is properly closed
            if 'cursor' in locals() and cursor:
                try:
                    cursor.close()
                except:
                    pass
            # Note: Don't close the main connection as it's managed by db_connection

    def is_date_data_available(self, check_date, latest_available_date=None):
        """
        Check if data is available for a specific date
        
        Args:
            check_date: Date to check (datetime.date object)
            latest_available_date: Latest available date (optional, will query if not provided)
        
        Returns:
            bool: True if data is available, False otherwise
        """
        try:
            if latest_available_date is None:
                latest_available_date = self.get_latest_available_data_date()
            
            if latest_available_date is None:
                # If we can't determine latest date, assume data is available
                return True
            
            # Convert check_date to date object if needed
            if isinstance(check_date, str):
                check_date = datetime.strptime(check_date, '%Y-%m-%d').date()
            elif hasattr(check_date, 'date'):
                check_date = check_date.date()
            
            return check_date <= latest_available_date
            
        except Exception as e:
            logger.error(f"❌ Error checking data availability for {check_date}: {str(e)}")
            return True  # Default to available on error

    def get_available_dates_in_month(self, year, month):
        """
        Get list of dates in the specified month that have data available
        
        Args:
            year: Year (int)
            month: Month (int)
        
        Returns:
            tuple: (available_dates_list, latest_available_date, total_days_in_month)
        """
        try:
            # Get latest available data date
            latest_available_date = self.get_latest_available_data_date()
            
            # Get total days in month
            if month == 12:
                next_month = datetime(year + 1, 1, 1)
            else:
                next_month = datetime(year, month + 1, 1)
            last_day_of_month = (next_month - timedelta(days=1)).day
            
            # Generate list of available dates
            available_dates = []
            for day in range(1, last_day_of_month + 1):
                current_date = date(year, month, day)
                if self.is_date_data_available(current_date, latest_available_date):
                    available_dates.append(current_date)
            
            logger.info(f"📅 Month {year}-{month:02d}: {len(available_dates)} of {last_day_of_month} days have data available")
            if latest_available_date:
                logger.info(f"   📊 Latest available data: {latest_available_date}")
            
            return available_dates, latest_available_date, last_day_of_month
            
        except Exception as e:
            logger.error(f"❌ Error getting available dates for {year}-{month}: {str(e)}")
            return [], None, 0 
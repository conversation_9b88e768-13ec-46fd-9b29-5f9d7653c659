# System Architecture: Design Patterns & Structure

## System Architecture

### High-Level Structure
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │────│   Flask App     │────│   SQL Server    │
│  (Frontend UI)  │    │  (web_app.py)   │    │  (VenusHR14)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────────────┐       ┌──────────────┐        ┌──────────────┐
    │ static/    │       │ src/         │        │ Database     │
    │ app.js     │       │ attendance_  │        │ Tables:      │
    │ (Frontend) │       │ reporter.py  │        │ - TAMachine  │
    │            │       │ (Core Logic) │        │ - EmployeePI │
    └────────────┘       └──────────────┘        │ - Overtime   │
                                                 └──────────────┘
         │
         │
    ┌────────────┐
    │ Google     │
    │ Apps       │
    │ Script     │
    │ (Sync)     │
    └────────────┘
```

### Component Relationships

#### Core Backend Components
1. **`web_app.py`** (Flask Application)
   - REST API endpoints for data access
   - Request handling and response formatting
   - Export functionality coordination
   - Static file serving

2. **`src/attendance_reporter.py`** (Business Logic)
   - Database connection management
   - Working hours calculation with business rules
   - Data aggregation and formatting
   - Export data preparation

3. **`src/main.py`** (Entry Point)
   - Application initialization
   - Configuration management

#### Frontend Components
1. **`templates/index.html`** (UI Structure)
   - Bootstrap-based responsive layout
   - Tab-based navigation (Monthly/Custom)
   - Grid display containers
   - Export controls

2. **`static/app.js`** (Frontend Logic)
   - API communication
   - Grid rendering and formatting
   - Color coding logic
   - Export functionality
   - Google Sheets sync integration

## Key Design Patterns

### Data Access Pattern
```python
# Read-Only Database Access Pattern
def get_attendance_data(business_code, start_date, end_date):
    """
    Consistent pattern for all database queries:
    1. Build parameterized SQL query
    2. Execute with proper error handling
    3. Return structured data
    4. No INSERT/UPDATE/DELETE operations
    """
    query = """
        SELECT DISTINCT 
            t.EmployeeID,
            e.EmployeeName,
            t.AttendDate,
            -- ... other fields
        FROM HR_T_TAMachine_Summary t
        LEFT JOIN HR_M_EmployeePI e ON t.EmployeeID = e.EmployeeID
        LEFT JOIN HR_T_Overtime o ON t.EmployeeID = o.EmployeeID 
            AND t.AttendDate = o.OTDate
        WHERE t.BusinessCode = ? 
            AND t.AttendDate BETWEEN ? AND ?
    """
```

### Business Rule Pattern
```python
# Centralized Business Rule Implementation
def calculate_working_hours_from_record(record):
    """
    Single source of truth for working hours calculation:
    1. Apply maximum hour limits by day type
    2. Format display as "(regular) | (overtime)"
    3. Handle special cases (Sunday, holidays)
    """
    day_of_week = record['AttendDate'].weekday()
    
    if day_of_week == 6:  # Sunday
        # Sunday logic: check overtime first
        if overtime_hours > 0:
            return f"(0) | ({overtime_hours})"
        return "OFF"
    
    # Apply business rules for max hours
    max_regular = 5 if day_of_week == 5 else 7  # Saturday: 5h, Others: 7h
    regular_hours = min(calculated_hours, max_regular)
    
    return f"({regular_hours}) | ({overtime_hours if overtime_hours > 0 else '-'})"
```

### Grid Display Pattern
```javascript
// Consistent Grid Rendering Pattern
function displayMonthlyGrid(data) {
    /*
    Standard grid rendering approach:
    1. Build header with date columns
    2. Create employee rows with attendance cells
    3. Apply color coding based on business rules
    4. Add totals row with aggregated data
    5. Maintain sticky header positioning
    */
    
    // Header generation
    const dates = generateDateRange(data.start_date, data.end_date);
    const headerHtml = buildHeaderRow(dates);
    
    // Employee rows
    const employeeRows = data.employees.map(emp => 
        buildEmployeeRow(emp, dates, data.attendance)
    );
    
    // Apply styling and interactivity
    applyColorCoding();
    enableStickyHeaders();
}
```

### Export Pattern
```python
# Unified Export Architecture
class ExportHandler:
    """
    Common interface for different export formats:
    1. Data preparation
    2. Format-specific rendering
    3. Response generation
    """
    
    def export_grid_excel(self, grid_data, format_type):
        workbook = xlsxwriter.Workbook()
        worksheet = workbook.add_worksheet()
        
        # Apply consistent formatting
        self.apply_excel_formatting(worksheet, grid_data)
        
        return workbook
    
    def export_grid_json(self, grid_data):
        return {
            'metadata': self.generate_metadata(),
            'data': self.format_json_data(grid_data)
        }
```

## Component Design Principles

### Separation of Concerns
- **Database Layer**: Pure data access without business logic
- **Business Layer**: Working hours calculation and rule enforcement
- **Presentation Layer**: UI rendering and user interaction
- **Integration Layer**: External system communication (Google Sheets)

### Single Responsibility
- Each function handles one specific aspect of functionality
- Clear naming conventions indicate purpose
- Minimal dependencies between components

### Data Flow Architecture
```
User Action → Frontend Event → API Call → Business Logic → Database Query → Response Processing → UI Update
```

### Error Handling Strategy
```python
# Consistent Error Handling Pattern
try:
    # Database operation
    result = execute_query(query, params)
    return {'success': True, 'data': result}
except DatabaseError as e:
    logger.error(f"Database error: {e}")
    return {'success': False, 'error': 'Database connection failed'}
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    return {'success': False, 'error': 'Internal server error'}
```

## Integration Patterns

### Google Apps Script Integration
```javascript
// Standardized Sync Pattern
async function syncToGoogleSheets(data, scriptUrl) {
    const payload = {
        action: 'updateAttendance',
        data: formatDataForSync(data),
        metadata: generateSyncMetadata()
    };
    
    const response = await fetch(scriptUrl, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(payload)
    });
    
    return handleSyncResponse(response);
}
```

### API Endpoint Pattern
```python
# Consistent API Structure
@app.route('/api/<endpoint>', methods=['GET', 'POST'])
def api_endpoint():
    try:
        # Validate request
        params = validate_request_params(request)
        
        # Process business logic
        result = process_business_logic(params)
        
        # Format response
        return jsonify({
            'success': True,
            'data': result,
            'timestamp': datetime.now().isoformat()
        })
    except ValidationError as e:
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': 'Internal error'}), 500
```

## Performance Patterns

### Database Query Optimization
- Use of parameterized queries to prevent SQL injection
- LEFT JOIN strategy for optional overtime data
- DISTINCT clause to eliminate duplicate records
- Date range filtering at database level

### Frontend Optimization
- Lazy loading for large datasets
- Sticky header CSS optimization
- Efficient DOM manipulation
- Minimal JavaScript library dependencies

### Caching Strategy (Future Implementation)
- Query result caching for frequently accessed months
- Browser-side caching for static resources
- Intelligent cache invalidation 
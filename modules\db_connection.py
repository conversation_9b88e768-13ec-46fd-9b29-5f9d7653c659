"""
Database Connection Module for VenusHR14 System
Supports both local and remote SQL Server connections with automatic fallback.
"""

import pyodbc
import logging
import json
import os
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseConnection:
    """
    Enhanced database connection class with support for both local and remote databases.
    Includes automatic fallback, connection pooling, and error handling.
    """
    
    def __init__(self, config_file: str = None):
        """
        Initialize database connection with configuration.
        
        Args:
            config_file: Path to configuration file. If None, uses default config.json
        """
        self.connection = None
        self.config_file = config_file or os.path.join(os.path.dirname(__file__), '../config.json')
        self.config = self._load_config()
        self.db_config = self.config.get("database_config", {})
        self.connection_mode = self.db_config.get("connection_mode", "local")
        self.fallback_enabled = self.db_config.get("fallback_enabled", True)
        self.connection_timeout = self.db_config.get("connection_timeout", 30)
        
        # Connection status tracking
        self.last_connection_attempt = None
        self.connection_errors = []
        
        logger.info(f"DatabaseConnection initialized with mode: {self.connection_mode}")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from config.json file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_file}")
                return config
            else:
                logger.warning(f"Config file not found: {self.config_file}, using default config")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}, using default config")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default database configuration."""
        return {
            "database_config": {
                "connection_mode": "local",
                "fallback_enabled": True,
                "connection_timeout": 30,
                "local_database": {
                    "server": "localhost",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "windows0819",
                    "port": 1433,
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "remote_database": {
                    "server": "********",
                    "database": "VenusHR14", 
                    "username": "sa",
                    "password": "supp0rt@",
                    "port": 1888,
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            }
        }
    
    def _build_connection_string(self, mode: str = None) -> str:
        """
        Build connection string for specified mode.
        
        Args:
            mode: 'local' or 'remote'. If None, uses current connection_mode.
            
        Returns:
            Connection string for SQL Server
        """
        mode = mode or self.connection_mode
        
        if mode == "local":
            config = self.db_config.get("local_database", {})
        elif mode == "remote":
            config = self.db_config.get("remote_database", {})
        else:
            raise ValueError(f"Invalid mode: {mode}. Must be 'local' or 'remote'")
        
        server = config.get("server", "localhost")
        port = config.get("port", 1433)
        database = config.get("database", "VenusHR14")
        username = config.get("username", "sa")
        password = config.get("password", "")
        driver = config.get("driver", "ODBC Driver 17 for SQL Server")
        
        connection_string = (
            f"DRIVER={{{driver}}};"
            f"SERVER={server},{port};"
            f"DATABASE={database};"
            f"UID={username};"
            f"PWD={password};"
            f"TrustServerCertificate=yes;"
            f"Encrypt=yes;"
        )
        
        logger.debug(f"Built connection string for {mode} mode")
        return connection_string
    
    def test_connection(self, mode: str = None) -> Tuple[bool, str]:
        """
        Test database connection for specified mode.
        
        Args:
            mode: 'local' or 'remote'. If None, uses current connection_mode.
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        mode = mode or self.connection_mode
        
        try:
            connection_string = self._build_connection_string(mode)
            logger.info(f"Testing {mode} database connection...")
            
            # Test connection with timeout
            test_conn = pyodbc.connect(connection_string, timeout=self.connection_timeout)
            test_conn.close()
            
            message = f"{mode.title()} database connection successful"
            logger.info(f"✅ {message}")
            return True, message
            
        except Exception as e:
            error_msg = f"{mode.title()} database connection failed: {str(e)}"
            logger.warning(f"❌ {error_msg}")
            return False, error_msg
    
    def connect(self, force_mode: str = None) -> bool:
        """
        Establish database connection with automatic fallback.
        
        Args:
            force_mode: Force specific connection mode ('local' or 'remote')
            
        Returns:
            True if connection successful, False otherwise
        """
        self.last_connection_attempt = datetime.now()
        
        # Try primary connection mode
        primary_mode = force_mode or self.connection_mode
        
        try:
            # Test connection first
            success, error_msg = self.test_connection(primary_mode)
            if success:
                connection_string = self._build_connection_string(primary_mode)
                self.connection = pyodbc.connect(connection_string, timeout=self.connection_timeout)
                logger.info(f"✅ Connected to {primary_mode} database")
                return True
            else:
                logger.warning(f"Primary connection ({primary_mode}) failed: {error_msg}")
                
                # Try fallback if enabled and not forced mode
                if self.fallback_enabled and not force_mode:
                    fallback_mode = "remote" if primary_mode == "local" else "local"
                    logger.info(f"Attempting fallback to {fallback_mode} database...")
                    
                    success, error_msg = self.test_connection(fallback_mode)
                    if success:
                        connection_string = self._build_connection_string(fallback_mode)
                        self.connection = pyodbc.connect(connection_string, timeout=self.connection_timeout)
                        logger.info(f"✅ Connected to {fallback_mode} database (fallback)")
                        # Update current mode to successful fallback
                        self.connection_mode = fallback_mode
                        return True
                    else:
                        logger.error(f"Fallback connection ({fallback_mode}) also failed: {error_msg}")
                
                # Record error
                self.connection_errors.append({
                    'timestamp': self.last_connection_attempt.isoformat(),
                    'primary_mode': primary_mode,
                    'primary_error': error_msg,
                    'fallback_attempted': self.fallback_enabled and not force_mode,
                    'fallback_success': False
                })
                
                return False
                
        except Exception as e:
            error_msg = f"Connection error: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            self.connection_errors.append({
                'timestamp': self.last_connection_attempt.isoformat(),
                'error': error_msg,
                'mode': primary_mode
            })
            
            return False
    
    def disconnect(self):
        """Close database connection."""
        if self.connection:
            try:
                self.connection.close()
                logger.debug("Database connection closed")
            except Exception as e:
                logger.warning(f"Error closing connection: {e}")
            finally:
                self.connection = None
    
    def execute_query(self, query: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results as list of dictionaries.
        Creates a fresh connection for each query to avoid concurrency issues.
        
        Args:
            query: SQL query string
            params: Query parameters tuple
            
        Returns:
            List of dictionaries representing query results
        """
        # Create a fresh connection for this query to avoid concurrency issues
        connection = None
        try:
            # Get a fresh connection
            connection_string = self._build_connection_string(self.connection_mode)
            connection = pyodbc.connect(connection_string)
            
            cursor = connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Get column names
            columns = [column[0] for column in cursor.description]
            
            # Fetch all rows and convert to dictionaries
            rows = cursor.fetchall()
            results = []
            
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    row_dict[columns[i]] = value
                results.append(row_dict)
            
            cursor.close()
            logger.debug(f"Query executed successfully, returned {len(results)} rows")
            return results
            
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
        finally:
            # Always close the connection when done
            if connection:
                try:
                    connection.close()
                except:
                    pass
    
    def execute_non_query(self, query: str, params: Tuple = None) -> int:
        """
        Execute an INSERT, UPDATE, or DELETE query.
        Creates a fresh connection for each query to avoid concurrency issues.
        
        Args:
            query: SQL query string
            params: Query parameters tuple
            
        Returns:
            Number of affected rows
        """
        # Create a fresh connection for this query to avoid concurrency issues
        connection = None
        try:
            # Get a fresh connection
            connection_string = self._build_connection_string(self.connection_mode)
            connection = pyodbc.connect(connection_string)
            
            cursor = connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            affected_rows = cursor.rowcount
            connection.commit()
            cursor.close()
            
            logger.debug(f"Non-query executed successfully, {affected_rows} rows affected")
            return affected_rows
            
        except Exception as e:
            logger.error(f"Error executing non-query: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            if connection:
                connection.rollback()
            raise
        finally:
            # Always close the connection when done
            if connection:
                try:
                    connection.close()
                except:
                    pass
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get current connection status and health information.
        
        Returns:
            Dictionary with connection status details
        """
        status = {
            'connected': self.connection is not None,
            'connection_mode': self.connection_mode,
            'fallback_enabled': self.fallback_enabled,
            'last_attempt': self.last_connection_attempt.isoformat() if self.last_connection_attempt else None,
            'recent_errors': self.connection_errors[-5:] if self.connection_errors else [],
            'config_file': self.config_file,
            'local_config': {
                'server': self.db_config.get('local_database', {}).get('server', 'localhost'),
                'database': self.db_config.get('local_database', {}).get('database', 'VenusHR14'),
                'port': self.db_config.get('local_database', {}).get('port', 1433)
            },
            'remote_config': {
                'server': self.db_config.get('remote_database', {}).get('server', '********'),
                'database': self.db_config.get('remote_database', {}).get('database', 'VenusHR14'),
                'port': self.db_config.get('remote_database', {}).get('port', 1888)
            }
        }
        
        return status
    
    def switch_mode(self, new_mode: str) -> Tuple[bool, str]:
        """
        Switch connection mode and reconnect.
        
        Args:
            new_mode: 'local' or 'remote'
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        if new_mode not in ['local', 'remote']:
            return False, f"Invalid mode: {new_mode}. Must be 'local' or 'remote'"
        
        # Disconnect current connection
        self.disconnect()
        
        # Test new connection
        success, message = self.test_connection(new_mode)
        if success:
            self.connection_mode = new_mode
            logger.info(f"Successfully switched to {new_mode} mode")
            return True, f"Successfully switched to {new_mode} database"
        else:
            return False, f"Failed to switch to {new_mode} database: {message}"
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
    
    def __del__(self):
        """Destructor - ensure connection is closed."""
        self.disconnect()


# Convenience function for quick database operations
def get_database_connection(config_file: str = None) -> DatabaseConnection:
    """
    Get a database connection instance.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        DatabaseConnection instance
    """
    return DatabaseConnection(config_file)


# Test function
def test_database_connections(config_file: str = None) -> Dict[str, Any]:
    """
    Test both local and remote database connections.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Dictionary with test results
    """
    db = DatabaseConnection(config_file)
    
    results = {
        'local': db.test_connection('local'),
        'remote': db.test_connection('remote'),
        'config': db.get_connection_status()
    }
    
    return results 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Leave Display</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Leave type styling */
        .hours-on-leave {
            background-color: #e8f5e8 !important;
            color: #2e7d32 !important;
            font-weight: bold;
        }

        /* Specific leave type colors */
        .leave-ct {
            background-color: #e3f2fd !important;
            color: #1976d2 !important;
        }

        .leave-h2 {
            background-color: #fce4ec !important;
            color: #c2185b !important;
        }

        .leave-p1 {
            background-color: #f3e5f5 !important;
            color: #7b1fa2 !important;
        }

        .leave-p2 {
            background-color: #e8f5e8 !important;
            color: #388e3c !important;
        }

        .leave-p3 {
            background-color: #fff3e0 !important;
            color: #f57c00 !important;
        }

        .leave-cb {
            background-color: #e1f5fe !important;
            color: #0277bd !important;
        }

        .leave-s {
            background-color: #ffebee !important;
            color: #d32f2f !important;
        }

        .leave-i {
            background-color: #f9fbe7 !important;
            color: #689f38 !important;
        }

        /* ALFA styling */
        .hours-alfa {
            background-color: #ffcdd2 !important;
            color: #d32f2f !important;
            font-weight: bold;
            border: 2px solid #d32f2f !important;
        }

        /* OFF styling */
        .hours-off {
            background-color: #f5f5f5 !important;
            color: #757575 !important;
        }

        .test-cell {
            width: 60px;
            height: 40px;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #ddd;
            padding: 5px;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1>Frontend Leave Display Test</h1>
        
        <div class="row mb-4">
            <div class="col-12">
                <h3>Leave Type Code Display Test</h3>
                <p>Testing how leave type codes will appear in the grid:</p>
                
                <div class="mb-3">
                    <h5>Leave Types:</h5>
                    <div class="test-cell hours-on-leave leave-ct">CT</div>
                    <div class="test-cell hours-on-leave leave-h2">H2</div>
                    <div class="test-cell hours-on-leave leave-p1">P1</div>
                    <div class="test-cell hours-on-leave leave-p2">P2</div>
                    <div class="test-cell hours-on-leave leave-p3">P3</div>
                    <div class="test-cell hours-on-leave leave-cb">CB</div>
                    <div class="test-cell hours-on-leave leave-s">S</div>
                    <div class="test-cell hours-on-leave leave-i">I</div>
                </div>
                
                <div class="mb-3">
                    <h5>Special Status:</h5>
                    <div class="test-cell hours-alfa">ALFA</div>
                    <div class="test-cell hours-off">OFF</div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <h3>API Test</h3>
                <button class="btn btn-primary" onclick="testAPI()">Test Monthly Grid API</button>
                <div id="apiResults" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function testAPI() {
            $('#apiResults').html('<div class="alert alert-info">Testing API...</div>');
            
            $.ajax({
                url: '/api/monthly-grid',
                method: 'GET',
                data: {
                    year: 2025,
                    month: 6,
                    bus_code: 'PTRJ'
                },
                success: function(response) {
                    console.log('API Response:', response);
                    
                    if (response.success) {
                        const gridData = response.data;
                        let leaveFound = 0;
                        let leaveExamples = [];
                        
                        // Check for leave data
                        gridData.grid_data.forEach(employee => {
                            const days = employee.days || {};
                            Object.keys(days).forEach(day => {
                                const dayData = days[day];
                                if (dayData.status === 'on_leave' || dayData.is_on_leave || dayData.leave_data) {
                                    leaveFound++;
                                    leaveExamples.push({
                                        employee: employee.EmployeeName,
                                        day: day,
                                        status: dayData.status,
                                        leave_data: dayData.leave_data
                                    });
                                }
                            });
                        });
                        
                        let resultHtml = `
                            <div class="alert alert-success">
                                <h5>✅ API Test Results:</h5>
                                <p><strong>Total employees:</strong> ${gridData.total_employees}</p>
                                <p><strong>Leave records found:</strong> ${leaveFound}</p>
                                <p><strong>Display format:</strong> ${gridData.display_format}</p>
                            </div>
                        `;
                        
                        if (leaveExamples.length > 0) {
                            resultHtml += '<div class="alert alert-info"><h6>Leave Examples:</h6><ul>';
                            leaveExamples.slice(0, 5).forEach(example => {
                                const leaveCode = example.leave_data ? example.leave_data.leave_type_code : 'Unknown';
                                resultHtml += `<li>${example.employee} (Day ${example.day}): Status=${example.status}, Leave=${leaveCode}</li>`;
                            });
                            resultHtml += '</ul></div>';
                        } else {
                            resultHtml += '<div class="alert alert-warning">❌ No leave data found in API response!</div>';
                        }
                        
                        $('#apiResults').html(resultHtml);
                    } else {
                        $('#apiResults').html(`<div class="alert alert-danger">❌ API Error: ${response.error}</div>`);
                    }
                },
                error: function(xhr, status, error) {
                    $('#apiResults').html(`<div class="alert alert-danger">❌ Request failed: ${error}</div>`);
                }
            });
        }
    </script>
</body>
</html>

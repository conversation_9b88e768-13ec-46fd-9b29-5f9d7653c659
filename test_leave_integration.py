#!/usr/bin/env python3
"""
Test script for leave data integration
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from modules.attendance_reporter import AttendanceReporter
from modules.db_connection import DatabaseConnection
import json

def test_leave_integration():
    """Test the leave data integration functionality"""
    
    print("🧪 Testing Leave Data Integration")
    print("=" * 50)
    
    try:
        # Initialize database connection
        db_conn = DatabaseConnection()
        if not db_conn.connect():
            print("❌ Failed to connect to database")
            return False
        
        print("✅ Database connection established")
        
        # Initialize attendance reporter
        reporter = AttendanceReporter()
        print("✅ AttendanceReporter initialized")
        
        # Test leave data retrieval
        print("\n📋 Testing leave data retrieval...")
        start_date = "2025-06-01"
        end_date = "2025-06-30"
        bus_code = "PTRJ"
        
        leave_data = reporter.get_leave_data(start_date, end_date, bus_code)
        print(f"   Retrieved {len(leave_data)} leave records")
        
        if leave_data:
            print("   Sample leave record:")
            sample = leave_data[0]
            for key, value in sample.items():
                print(f"     {key}: {value}")
        
        # Test leave type description mapping
        print("\n🏷️  Testing leave type descriptions...")
        test_codes = ['CT', 'H2', 'P1', 'P2', 'P3', 'XX']
        for code in test_codes:
            description = reporter.get_leave_type_description(code)
            print(f"   {code} -> {description}")
        
        # Test enhanced attendance data with leave integration
        print("\n📊 Testing enhanced attendance data...")
        attendance_data = reporter.get_attendance_data(start_date, end_date, bus_code)
        print(f"   Retrieved {len(attendance_data)} attendance records")
        
        # Count records with different statuses
        status_counts = {}
        leave_counts = {}
        
        for record in attendance_data:
            status = record.get('Status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            leave_data_rec = record.get('LeaveData')
            if leave_data_rec:
                leave_type = leave_data_rec.get('leave_type_code', 'unknown')
                leave_counts[leave_type] = leave_counts.get(leave_type, 0) + 1
        
        print("   Status distribution:")
        for status, count in status_counts.items():
            print(f"     {status}: {count}")
        
        if leave_counts:
            print("   Leave type distribution:")
            for leave_type, count in leave_counts.items():
                print(f"     {leave_type}: {count}")
        
        # Test monthly grid with leave integration
        print("\n📅 Testing monthly grid with leave integration...")
        grid_data = reporter.get_monthly_attendance_grid(2025, 6, bus_code)
        print(f"   Grid generated for {grid_data.get('total_employees', 0)} employees")
        print(f"   Days in month: {grid_data.get('days_in_month', 0)}")
        print(f"   Display format: {grid_data.get('display_format', 'unknown')}")
        
        # Check for enhanced features in grid data
        if grid_data.get('grid_data'):
            sample_employee = grid_data['grid_data'][0]
            sample_day = list(sample_employee['days'].values())[0]
            
            print("   Sample day data structure:")
            for key, value in sample_day.items():
                if key not in ['raw_data']:  # Skip raw_data for brevity
                    print(f"     {key}: {value}")
        
        print("\n✅ Leave integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'db_conn' in locals():
            db_conn.disconnect()

if __name__ == "__main__":
    success = test_leave_integration()
    sys.exit(0 if success else 1)

# Venus Attendance Report Web Application
## Self-Contained Installation Guide

### 🚀 Quick Start

1. **Extract** the project folder to your desired location
2. **Run** `install_complete.bat` to install all dependencies
3. **Run** `python web_app.py` to start the application
4. **Access** the application at `http://localhost:5173`

---

### 📋 Prerequisites

- **Python 3.8+** installed on your system
- **SQL Server ODBC Driver 17** (for database connectivity)
- **Internet connection** (for initial dependency installation)

---

### 🏗️ Project Structure

```
attendance_report_web_staging/
├── modules/                    # Self-contained internal modules
│   ├── __init__.py
│   ├── db_connection.py       # Database connection with local/remote support
│   ├── export_manager.py      # Excel/JSON export functionality  
│   └── attendance_reporter.py # Main reporting engine
├── templates/                 # HTML templates
├── static/                   # CSS, JavaScript, assets
├── data/                     # Local database files (staging)
├── exports/                  # Generated reports
├── config.json              # Database and application configuration
├── web_app.py               # Main Flask application
├── requirements_complete.txt # All Python dependencies
└── install_complete.bat     # Installation script
```

---

### ⚙️ Database Configuration

The application supports **both local and remote** database connections with automatic fallback:

#### Local Database (Default)
- **Server**: localhost:1433
- **Database**: VenusHR14
- **Username**: sa
- **Password**: windows0819

#### Remote Database
- **Server**: ********:1888
- **Database**: VenusHR14
- **Username**: sa
- **Password**: supp0rt@

#### Configuration File (`config.json`)
```json
{
  "database_config": {
    "connection_mode": "local",
    "fallback_enabled": true,
    "local_database": {
      "server": "localhost",
      "port": 1433,
      "database": "VenusHR14",
      "username": "sa",
      "password": "windows0819"
    },
    "remote_database": {
      "server": "********",
      "port": 1888,
      "database": "VenusHR14",
      "username": "sa",
      "password": "supp0rt@"
    }
  }
}
```

---

### 🔄 Installation Steps

#### Method 1: Automatic Installation (Recommended)
```bash
# Double-click or run in command prompt:
install_complete.bat
```

#### Method 2: Manual Installation
```bash
# Install dependencies
pip install -r requirements_complete.txt

# Run the application
python web_app.py
```

---

### 🚦 Running the Application

1. **Start the server**:
   ```bash
   python web_app.py
   ```

2. **Open your browser** and navigate to:
   ```
   http://localhost:5173
   ```

3. **Database Connection**: The app will automatically test both local and remote connections and use the best available option.

---

### 📊 Features

- **Monthly Attendance Reports**: Grid view with employee vs days matrix
- **Date Range Reports**: Custom period attendance analysis  
- **Excel Export**: Professional formatted reports
- **Local/Remote Database**: Automatic failover between database servers
- **Staging System**: Local data staging for offline work
- **Employee Charge Jobs**: Integration with Google Apps Script

---

### 🔧 Troubleshooting

#### Database Connection Issues
1. **Check ODBC Driver**: Ensure "ODBC Driver 17 for SQL Server" is installed
2. **Test Connection**: Use the web interface to test both local and remote connections
3. **Check Firewall**: Ensure SQL Server ports (1433, 1888) are accessible
4. **Verify Credentials**: Check username/password in `config.json`

#### Module Import Errors
- The project is now **self-contained** with all modules in the `modules/` directory
- No external module paths needed - everything is included

#### Port Conflicts
- Default port is 5173
- Change in `config.json` under `server_config.port` if needed

---

### 🔒 Security Notes

- Database passwords are stored in `config.json`
- Consider using environment variables for production
- Local staging database is created automatically in `data/` directory

---

### 🚀 Deployment to Other Computers

1. **Copy the entire project folder** to the target computer
2. **Install Python 3.8+** if not already installed
3. **Install SQL Server ODBC Driver 17**
4. **Run** `install_complete.bat`
5. **Update** `config.json` with correct database settings if needed
6. **Run** `python web_app.py`

The application is now **completely self-contained** and will work on any Windows computer with Python installed!

---

### 📞 Support

For issues or questions:
- Check the console output for detailed error messages
- Verify database connectivity using the built-in connection tester
- Ensure all dependencies are correctly installed 
#!/usr/bin/env python3
"""
Database migration script to fix the staging database schema.
This script adds missing columns to the existing staging_attendance table.
"""

import sqlite3
import os
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_staging_database():
    """Fix the staging database by adding missing columns."""
    try:
        # Path to staging database
        staging_db_path = os.path.join('data', 'staging_attendance.db')
        
        if not os.path.exists(staging_db_path):
            logger.error(f"Staging database not found at {staging_db_path}")
            return False
            
        logger.info(f"Connecting to staging database: {staging_db_path}")
        conn = sqlite3.connect(staging_db_path)
        cursor = conn.cursor()
        
        # Get current table structure
        cursor.execute("PRAGMA table_info(staging_attendance)")
        columns = [column[1] for column in cursor.fetchall()]
        logger.info(f"Current columns: {columns}")
        
        # Define required columns and their types
        required_columns = {
            'station_code': 'TEXT',
            'task_code': 'TEXT',
            'machine_code': 'TEXT', 
            'expense_code': 'TEXT',
            'total_hours': 'REAL DEFAULT 0',
            'source_record_id': 'TEXT',
            'notes': 'TEXT'
        }
        
        # Add missing columns
        added_columns = []
        for col_name, col_type in required_columns.items():
            if col_name not in columns:
                logger.info(f"Adding missing column: {col_name} {col_type}")
                try:
                    cursor.execute(f'ALTER TABLE staging_attendance ADD COLUMN {col_name} {col_type}')
                    added_columns.append(col_name)
                except Exception as e:
                    logger.error(f"Failed to add column {col_name}: {e}")
                    
        # Commit changes
        conn.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(staging_attendance)")
        new_columns = [column[1] for column in cursor.fetchall()]
        logger.info(f"Updated columns: {new_columns}")
        
        conn.close()
        
        if added_columns:
            logger.info(f"Successfully added columns: {added_columns}")
        else:
            logger.info("No columns needed to be added")
            
        return True
        
    except Exception as e:
        logger.error(f"Error fixing staging database: {e}")
        return False

def recreate_staging_database():
    """Recreate the staging database with correct schema."""
    try:
        # Path to staging database
        staging_db_path = os.path.join('data', 'staging_attendance.db')
        
        # Backup existing data if database exists
        backup_data = []
        if os.path.exists(staging_db_path):
            logger.info("Backing up existing data...")
            conn = sqlite3.connect(staging_db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute("SELECT * FROM staging_attendance")
                backup_data = cursor.fetchall()
                logger.info(f"Backed up {len(backup_data)} records")
            except:
                logger.info("No existing data to backup")
            
            conn.close()
            
            # Remove old database
            os.remove(staging_db_path)
            logger.info("Removed old database")
        
        # Create directory if it doesn't exist
        os.makedirs('data', exist_ok=True)
        
        # Create new database with correct schema
        logger.info("Creating new staging database...")
        conn = sqlite3.connect(staging_db_path)
        cursor = conn.cursor()
        
        # Create staging table with complete schema
        cursor.execute('''
            CREATE TABLE staging_attendance (
                id TEXT PRIMARY KEY,
                employee_id TEXT NOT NULL,
                employee_name TEXT NOT NULL,
                date TEXT NOT NULL,
                day_of_week TEXT,
                shift TEXT,
                check_in TEXT,
                check_out TEXT,
                regular_hours REAL DEFAULT 0,
                overtime_hours REAL DEFAULT 0,
                total_hours REAL DEFAULT 0,
                task_code TEXT,
                station_code TEXT,
                machine_code TEXT,
                expense_code TEXT,
                status TEXT DEFAULT 'staged',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                source_record_id TEXT,
                notes TEXT
            )
        ''')
        
        # Create staging operations log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS staging_operations_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                operation_type TEXT NOT NULL,
                table_name TEXT NOT NULL,
                operation_details TEXT,
                affected_record_ids TEXT,
                data_volume INTEGER DEFAULT 0,
                user_identifier TEXT DEFAULT 'system',
                result_status TEXT NOT NULL,
                error_details TEXT,
                query_parameters TEXT,
                ip_address TEXT,
                user_agent TEXT
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_employee_id ON staging_attendance(employee_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_date ON staging_attendance(date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_status ON staging_attendance(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_created_at ON staging_attendance(created_at)')
        
        # Create indexes for log table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_timestamp ON staging_operations_log(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_operation_type ON staging_operations_log(operation_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_result_status ON staging_operations_log(result_status)')
        
        conn.commit()
        conn.close()
        
        logger.info("Successfully recreated staging database with correct schema")
        return True
        
    except Exception as e:
        logger.error(f"Error recreating staging database: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting staging database migration...")
    
    # Try to fix existing database first
    if fix_staging_database():
        logger.info("Database migration completed successfully")
    else:
        logger.info("Attempting to recreate database...")
        if recreate_staging_database():
            logger.info("Database recreation completed successfully")
        else:
            logger.error("Failed to fix staging database")
            exit(1)
    
    logger.info("Staging database is now ready for use")

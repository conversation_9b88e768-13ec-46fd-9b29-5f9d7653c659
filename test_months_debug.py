#!/usr/bin/env python3
"""
Standalone test script to diagnose the months loading issue
Run this to test database connection and query the months independently
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the modules directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'modules')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test database connection and query months data."""
    
    print("=" * 60)
    print("MONTHS LOADING DIAGNOSTIC TEST")
    print("=" * 60)
    
    try:
        # Import modules
        from modules.db_connection import DatabaseConnection
        from modules.attendance_reporter import AttendanceReporter
        
        print("✅ Modules imported successfully")
        
        # Test 1: Load configuration
        print("\n🔧 Test 1: Loading configuration...")
        config_file = "config.json"
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"✅ Configuration loaded from {config_file}")
            
            db_config = config.get('database_config', {})
            print(f"   Connection mode: {db_config.get('connection_mode')}")
            print(f"   Local server: {db_config.get('local_database', {}).get('server')}")
            print(f"   Local database: {db_config.get('local_database', {}).get('database')}")
        else:
            print(f"❌ Configuration file {config_file} not found")
            return False
        
        # Test 2: Create database connection
        print("\n🔌 Test 2: Creating database connection...")
        db_conn = DatabaseConnection(config_file)
        print("✅ DatabaseConnection object created")
        
        # Test 3: Test connection
        print("\n🧪 Test 3: Testing database connection...")
        success, message = db_conn.test_connection()
        if success:
            print(f"✅ Database connection test: {message}")
        else:
            print(f"❌ Database connection test failed: {message}")
            
            # Try fallback
            print("   Trying fallback connection...")
            fallback_mode = "remote" if db_conn.connection_mode == "local" else "local"
            success, message = db_conn.test_connection(fallback_mode)
            if success:
                print(f"✅ Fallback connection ({fallback_mode}): {message}")
                db_conn.connection_mode = fallback_mode
            else:
                print(f"❌ Fallback connection also failed: {message}")
                return False
        
        # Test 4: Establish connection
        print("\n🔗 Test 4: Establishing connection...")
        if db_conn.connect():
            print("✅ Database connection established")
        else:
            print("❌ Failed to establish database connection")
            return False
        
        # Test 5: Test basic query
        print("\n📊 Test 5: Testing basic query...")
        try:
            result = db_conn.execute_query("SELECT COUNT(*) as total_records FROM HR_T_TAMachine_Summary")
            total_records = result[0]['total_records'] if result else 0
            print(f"✅ Total records in HR_T_TAMachine_Summary: {total_records}")
        except Exception as e:
            print(f"❌ Basic query failed: {e}")
            return False
        
        # Test 6: Test PTRJ records
        print("\n🏢 Test 6: Testing PTRJ business code records...")
        try:
            result = db_conn.execute_query("SELECT COUNT(*) as ptrj_records FROM HR_T_TAMachine_Summary WHERE BusCode = 'PTRJ'")
            ptrj_records = result[0]['ptrj_records'] if result else 0
            print(f"✅ PTRJ records in HR_T_TAMachine_Summary: {ptrj_records}")
            
            if ptrj_records == 0:
                print("⚠️  WARNING: No records found for BusCode = 'PTRJ'")
                print("   Checking available business codes...")
                
                result = db_conn.execute_query("SELECT DISTINCT BusCode, COUNT(*) as count FROM HR_T_TAMachine_Summary GROUP BY BusCode")
                if result:
                    print("   Available business codes:")
                    for row in result:
                        print(f"     - {row['BusCode']}: {row['count']} records")
                else:
                    print("   No business codes found")
                    
        except Exception as e:
            print(f"❌ PTRJ query failed: {e}")
            return False
        
        # Test 7: Test months query
        print("\n📅 Test 7: Testing months query...")
        try:
            months_query = """
            SELECT 
                YEAR(t.TADate) as Year,
                MONTH(t.TADate) as Month,
                DATENAME(MONTH, t.TADate) as MonthName,
                COUNT(*) as RecordCount,
                COUNT(DISTINCT t.EmployeeID) as EmployeeCount,
                MIN(t.TADate) as FirstDate,
                MAX(t.TADate) as LastDate
            FROM 
                HR_T_TAMachine_Summary t
            WHERE 
                t.TADate IS NOT NULL
                AND t.BusCode = 'PTRJ'
            GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
            ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
            """
            
            months_result = db_conn.execute_query(months_query)
            print(f"✅ Months query executed successfully")
            print(f"   Found {len(months_result)} months with data")
            
            if months_result:
                print("   Available months:")
                for month in months_result[:5]:  # Show first 5 months
                    print(f"     - {month['MonthName']} {month['Year']}: {month['RecordCount']} records, {month['EmployeeCount']} employees")
                if len(months_result) > 5:
                    print(f"     ... and {len(months_result) - 5} more months")
            else:
                print("   ⚠️  No months found with data for PTRJ")
                
        except Exception as e:
            print(f"❌ Months query failed: {e}")
            return False
        
        # Test 8: Test AttendanceReporter
        print("\n📋 Test 8: Testing AttendanceReporter...")
        try:
            reporter = AttendanceReporter(config_file)
            print("✅ AttendanceReporter created successfully")
            
            months = reporter.get_available_months('PTRJ')
            print(f"✅ AttendanceReporter.get_available_months() returned {len(months)} months")
            
            if months:
                print("   Sample month data:")
                sample_month = months[0]
                for key, value in sample_month.items():
                    print(f"     {key}: {value}")
            
        except Exception as e:
            print(f"❌ AttendanceReporter test failed: {e}")
            return False
        
        # Close connection
        db_conn.disconnect()
        print("\n✅ All tests completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        print(f"   Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_database_connection()
    print("\n" + "=" * 60)
    if success:
        print("🎉 DIAGNOSIS COMPLETE: Database connection and months query working!")
        print("   The issue may be in the web application initialization or API routing.")
    else:
        print("❌ DIAGNOSIS FAILED: Database connection or query issues found.")
        print("   Please check the error messages above and fix the database configuration.")
    print("=" * 60) 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staging Transfer Debug Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Staging Transfer Debug Test</h1>
    
    <div>
        <h3>Test Local Staging Transfer</h3>
        <button id="testStagingTransfer">Test Transfer to Local Staging</button>
        <button id="testWithSampleData">Test with Sample Data</button>
        <button id="testEndpointOnly">Test Endpoint Only</button>
        <pre id="response"></pre>
    </div>

    <div>
        <h3>Sample Data</h3>
        <textarea id="sampleData" rows="10" cols="80">{
  "records": [
    {
      "employee_id": "PTRJ.241000001",
      "employee_name": "<PERSON>",
      "date": "2025-03-01",
      "day_of_week": "Saturday",
      "shift": "Regular",
      "check_in": "08:00",
      "check_out": "17:00",
      "regular_hours": 7.5,
      "overtime_hours": 1.5,
      "task_code": "ASSEMBLY",
      "station_code": "STN-01",
      "machine_code": "MCH-01",
      "expense_code": "EXP-001",
      "notes": "Test transfer from debug page"
    }
  ]
}</textarea>
    </div>

    <script>
        $('#testStagingTransfer').click(function() {
            // Test with minimal data structure
            const testData = {
                records: [
                    {
                        employee_id: "TEST001",
                        employee_name: "Test Employee",
                        date: "2025-03-01",
                        day_of_week: "Saturday",
                        shift: "Regular", 
                        check_in: "08:00",
                        check_out: "17:00",
                        regular_hours: 8.0,
                        overtime_hours: 0.0,
                        task_code: "TEST",
                        station_code: "STN-TEST",
                        machine_code: "MCH-TEST",
                        expense_code: "EXP-TEST",
                        notes: "Test record from debug page"
                    }
                ]
            };

            console.log('Sending test data:', testData);
            $('#response').text('Sending request...');

            $.ajax({
                url: '/api/staging/data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(testData),
                success: function(response) {
                    console.log('Success response:', response);
                    $('#response').text('SUCCESS:\n' + JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    console.error('Error response:', xhr);
                    let errorText = 'ERROR:\n';
                    errorText += 'Status: ' + xhr.status + '\n';
                    errorText += 'Error: ' + error + '\n';
                    
                    if (xhr.responseJSON) {
                        errorText += 'Response: ' + JSON.stringify(xhr.responseJSON, null, 2);
                    } else {
                        errorText += 'Response Text: ' + xhr.responseText;
                    }
                    
                    $('#response').text(errorText);
                }
            });
        });

        $('#testWithSampleData').click(function() {
            const sampleDataText = $('#sampleData').val();
            let sampleData;
            
            try {
                sampleData = JSON.parse(sampleDataText);
            } catch (e) {
                $('#response').text('ERROR: Invalid JSON in sample data\n' + e.message);
                return;
            }

            console.log('Sending sample data:', sampleData);
            $('#response').text('Sending request with sample data...');

            $.ajax({
                url: '/api/staging/data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(sampleData),
                success: function(response) {
                    console.log('Success response:', response);
                    $('#response').text('SUCCESS:\n' + JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    console.error('Error response:', xhr);
                    let errorText = 'ERROR:\n';
                    errorText += 'Status: ' + xhr.status + '\n';
                    errorText += 'Error: ' + error + '\n';
                    
                    if (xhr.responseJSON) {
                        errorText += 'Response: ' + JSON.stringify(xhr.responseJSON, null, 2);
                    } else {
                        errorText += 'Response Text: ' + xhr.responseText;
                    }
                    
                    $('#response').text(errorText);
                }
            });
        });

        $('#testEndpointOnly').click(function() {
            const testData = { test: "simple test" };

            console.log('Testing endpoint only with:', testData);
            $('#response').text('Testing endpoint...');

            $.ajax({
                url: '/api/staging/test',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(testData),
                success: function(response) {
                    console.log('Endpoint test success:', response);
                    $('#response').text('ENDPOINT TEST SUCCESS:\n' + JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    console.error('Endpoint test error:', xhr);
                    let errorText = 'ENDPOINT TEST ERROR:\n';
                    errorText += 'Status: ' + xhr.status + '\n';
                    errorText += 'Error: ' + error + '\n';
                    
                    if (xhr.responseJSON) {
                        errorText += 'Response: ' + JSON.stringify(xhr.responseJSON, null, 2);
                    } else {
                        errorText += 'Response Text: ' + xhr.responseText;
                    }
                    
                    $('#response').text(errorText);
                }
            });
        });
    </script>
</body>
</html> 
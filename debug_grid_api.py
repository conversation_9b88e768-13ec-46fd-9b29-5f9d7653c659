#!/usr/bin/env python3
"""
Debug script to check if leave data is properly returned by the monthly grid API
"""

import requests
import json

def debug_monthly_grid_api():
    """Debug the monthly grid API response for leave data"""
    
    print("🔍 Debugging Monthly Grid API for Leave Data")
    print("=" * 60)
    
    try:
        # Test the monthly grid API
        url = 'http://localhost:5173/api/monthly-grid'
        params = {
            'year': 2025,
            'month': 6,
            'bus_code': 'PTRJ'
        }
        
        print(f"📡 Calling API: {url}")
        print(f"📋 Parameters: {params}")
        
        response = requests.get(url, params=params, timeout=30)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {data.get('success')}")
            
            grid_data = data.get('data', {})
            employees = grid_data.get('grid_data', [])
            print(f"👥 Total employees: {len(employees)}")
            print(f"📅 Days in month: {grid_data.get('days_in_month', 0)}")
            print(f"🔧 Display format: {grid_data.get('display_format', 'unknown')}")
            
            # Look for employees with known leave data
            leave_found = 0
            employees_with_leave = []
            
            for employee in employees:
                employee_name = employee.get('EmployeeName', 'Unknown')
                employee_id = employee.get('EmployeeID', 'Unknown')
                days = employee.get('days', {})
                
                # Check all days for leave data
                for day in range(1, 31):
                    day_data = days.get(str(day))
                    if day_data:
                        status = day_data.get('status')
                        leave_data = day_data.get('leave_data')
                        is_on_leave = day_data.get('is_on_leave')
                        
                        if leave_data or status == 'on_leave' or is_on_leave:
                            leave_found += 1
                            employees_with_leave.append({
                                'employee': employee_name,
                                'employee_id': employee_id,
                                'day': day,
                                'status': status,
                                'leave_data': leave_data,
                                'is_on_leave': is_on_leave
                            })
            
            print(f"\n📝 Leave Records Found: {leave_found}")
            
            if employees_with_leave:
                print("\n🏷️  Employees with Leave Data:")
                for record in employees_with_leave[:10]:  # Show first 10
                    print(f"  👤 {record['employee']} ({record['employee_id']})")
                    print(f"     📅 Day {record['day']}: status={record['status']}")
                    print(f"     📋 Leave data: {record['leave_data']}")
                    print(f"     ✅ Is on leave: {record['is_on_leave']}")
                    print()
            else:
                print("❌ No leave data found in API response!")
                
                # Check a sample employee's day structure
                if employees:
                    sample_employee = employees[0]
                    sample_day = sample_employee.get('days', {}).get('1')
                    print(f"\n🔍 Sample day data structure for {sample_employee.get('EmployeeName')}:")
                    if sample_day:
                        for key, value in sample_day.items():
                            print(f"     {key}: {value}")
                    else:
                        print("     No day data found")
            
            # Check debug info
            debug_info = data.get('debug_info', {})
            print(f"\n🐛 Debug Info:")
            for key, value in debug_info.items():
                print(f"   {key}: {value}")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_monthly_grid_api()

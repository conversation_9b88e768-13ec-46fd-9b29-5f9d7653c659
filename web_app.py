"""
Flask web application for Attendance Report System.
Provides a web interface for viewing and exporting attendance reports.
"""

import os
import sys
import json
import requests
from datetime import datetime, date, timedelta
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
import sqlite3
import uuid
import pyodbc
import time

# Add the modules directory to the Python path for internal modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'modules')))

# Import internal modules
from modules.attendance_reporter import AttendanceReporter

app = Flask(__name__)
CORS(app)

# Configure Flask app
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['JSON_SORT_KEYS'] = False

class DatabaseConnectionManager:
    """
    Manages database connections with toggle functionality and fallback systems.
    Supports both local and remote SQL Server connections with automatic failover.
    """
    
    def __init__(self, config):
        self.config = config
        self.db_config = config.get("database_config", {})
        self.connection_mode = self.db_config.get("connection_mode", "local")
        self.fallback_enabled = self.db_config.get("fallback_enabled", True)
        self.current_connection = None
        self.connection_status = {
            "local": {"connected": False, "last_attempt": None, "error": None},
            "remote": {"connected": False, "last_attempt": None, "error": None}
        }
        
    def _build_connection_string(self, mode="local", config=None):
        """Build connection string for specified mode with optional custom config."""
        try:
            if mode == "local":
                # Use local database configuration
                local_config = self.db_config.get("local_database", {})
                server = local_config.get("server", "localhost")
                port = local_config.get("port", 1433)
                database = local_config.get("database", "VenusHR14")
                username = local_config.get("username", "sa")
                password = local_config.get("password", "windows0819")
                driver = local_config.get("driver", "ODBC Driver 17 for SQL Server")
                
                connection_string = (
                    f"DRIVER={{{driver}}};"
                    f"SERVER={server},{port};"
                    f"DATABASE={database};"
                    f"UID={username};"
                    f"PWD={password};"
                    f"TrustServerCertificate=yes;"
                    f"Encrypt=yes;"
                )
                
            elif mode == "remote":
                # Use custom config if provided, otherwise use stored remote config
                if config:
                    remote_config = config
                else:
                    remote_config = self.db_config.get("remote_database", {})
                
                if not remote_config or not remote_config.get("server"):
                    logger.warning("Remote database configuration not available")
                    return None
                
                server = remote_config.get("server")
                port = remote_config.get("port", 1888)
                database = remote_config.get("database", "VenusHR14")
                username = remote_config.get("username", "sa")
                password = remote_config.get("password")
                driver = remote_config.get("driver", "ODBC Driver 17 for SQL Server")
                
                if not password:
                    logger.warning("Remote database password not configured")
                    return None
                
                connection_string = (
                    f"DRIVER={{{driver}}};"
                    f"SERVER={server},{port};"
                    f"DATABASE={database};"
                    f"UID={username};"
                    f"PWD={password};"
                    f"TrustServerCertificate=yes;"
                    f"Encrypt=yes;"
                )
            else:
                logger.error(f"Unknown database mode: {mode}")
                return None
            
            logger.debug(f"Built connection string for {mode} mode")
            return connection_string
            
        except Exception as e:
            logger.error(f"Error building connection string for {mode}: {e}")
            return None
    
    def test_connection(self, mode="local", skip_during_init=False, config=None):
        """Test database connection for specified mode."""
        try:
            # Skip testing during initialization for faster startup
            if skip_during_init:
                logger.info(f"⏩ Skipping {mode} database connection test during initialization")
                self.connection_status[mode]["connected"] = None  # Unknown status
                self.connection_status[mode]["error"] = "Not tested during startup (fast boot mode)"
                self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
                return True, "Connection test skipped for fast startup"
            
            # Use appropriate timeout for connections - shorter for local to prevent hanging
            connection_timeout = 10 if mode == "remote" else 5  # 10 seconds for remote, 5 for local
            
            connection_string = self._build_connection_string(mode, config)
            if not connection_string:
                error_msg = f"Failed to build connection string for {mode} mode"
                self.connection_status[mode]["connected"] = False
                self.connection_status[mode]["error"] = error_msg
                self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
                return False, error_msg
            
            logger.info(f"🧪 Testing {mode} database connection (timeout: {connection_timeout}s)...")
            
            # Test connection with timeout
            start_time = time.time()
            conn = pyodbc.connect(connection_string, timeout=connection_timeout)
            conn.close()
            test_duration = time.time() - start_time
            
            # Update status on success
            self.connection_status[mode]["connected"] = True
            self.connection_status[mode]["error"] = None
            self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
            
            logger.info(f"✅ {mode.title()} database connection successful ({test_duration:.2f}s)")
            return True, f"Connection successful in {test_duration:.2f} seconds"
            
        except Exception as e:
            error_msg = f"Database connection failed: {str(e)}"
            self.connection_status[mode]["connected"] = False
            self.connection_status[mode]["error"] = error_msg
            self.connection_status[mode]["last_attempt"] = datetime.now().isoformat()
            
            logger.warning(f"❌ {mode.title()} database connection failed: {error_msg}")
            return False, error_msg
    
    def get_connection(self, force_mode=None):
        """
        Get database connection with automatic fallback logic.
        
        Args:
            force_mode: Force specific connection mode ("local" or "remote")
            
        Returns:
            pyodbc.Connection or None
        """
        mode_to_try = force_mode or self.connection_mode
        
        try:
            # Primary connection attempt
            success, error = self.test_connection(mode_to_try)
            if success:
                connection_string = self._build_connection_string(mode_to_try)
                conn = pyodbc.connect(connection_string)
                logger.info(f"✅ Connected to {mode_to_try} database")
                return conn
            
            # Fallback logic if primary connection fails
            if self.fallback_enabled and not force_mode:
                fallback_mode = "remote" if mode_to_try == "local" else "local"
                logger.warning(f"Primary connection ({mode_to_try}) failed, trying fallback ({fallback_mode})")
                
                success, error = self.test_connection(fallback_mode)
                if success:
                    connection_string = self._build_connection_string(fallback_mode)
                    conn = pyodbc.connect(connection_string)
                    logger.info(f"✅ Connected to {fallback_mode} database (fallback)")
                    # Update current mode to successful fallback
                    self.connection_mode = fallback_mode
                    return conn
                else:
                    logger.error(f"❌ Fallback connection ({fallback_mode}) also failed: {error}")
            
            # Both connections failed or fallback disabled
            logger.error(f"❌ Database connection failed: {error}")
            return None
            
        except Exception as e:
            logger.error(f"❌ Unexpected error in get_connection: {str(e)}")
            return None
    
    def switch_connection_mode(self, new_mode):
        """Switch connection mode and test new connection."""
        if new_mode not in ["local", "remote"]:
            return False, "Invalid connection mode"
        
        success, error = self.test_connection(new_mode)
        if success:
            self.connection_mode = new_mode
            logger.info(f"✅ Switched to {new_mode} database mode")
            return True, f"Successfully switched to {new_mode} database"
        else:
            return False, f"Failed to switch to {new_mode} database: {error}"
    
    def get_connection_status(self):
        """Get current connection status for both modes."""
        return {
            "current_mode": self.connection_mode,
            "fallback_enabled": self.fallback_enabled,
            "local_status": self.connection_status["local"],
            "remote_status": self.connection_status["remote"]
        }
    
    def update_database_config(self, new_config):
        """Update database configuration and save to config.json."""
        try:
            # Update internal configuration
            if "connection_mode" in new_config:
                self.connection_mode = new_config["connection_mode"]
            
            if "fallback_enabled" in new_config:
                self.fallback_enabled = new_config["fallback_enabled"]
            
            if "local_database" in new_config:
                self.db_config["local_database"].update(new_config["local_database"])
            
            if "remote_database" in new_config:
                self.db_config["remote_database"].update(new_config["remote_database"])
            
            # Save to config.json
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ Database configuration updated and saved")
            return True, "Configuration updated successfully"
            
        except Exception as e:
            logger.error(f"❌ Failed to update database configuration: {str(e)}")
            return False, f"Failed to update configuration: {str(e)}"

    def get_status(self):
        """Get current database connection status."""
        return {
            'current_mode': self.connection_mode,
            'fallback_enabled': self.db_config.get('fallback_enabled', True),
            'local_status': self.connection_status.get('local', {}),
            'remote_status': self.connection_status.get('remote', {}),
            'last_updated': datetime.now().isoformat()
        }

# Load configuration from config.json
def load_config():
    """Load configuration from config.json file."""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Ensure database_config section exists with proper structure
        if 'database_config' not in config:
            config['database_config'] = {
                "connection_mode": "local",
                "fallback_enabled": True,
                "connection_timeout": 30,
                "local_database": {
                    "server": "localhost",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "windows0819",
                    "port": 1433,
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "remote_database": {
                    "server": "********",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "supp0rt@",
                    "port": 1888,
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            }
        
        # Ensure local_database section exists
        if 'local_database' not in config['database_config']:
            config['database_config']['local_database'] = {
                "server": "localhost",
                "database": "VenusHR14",
                "username": "sa",
                "password": "windows0819",
                "port": 1433,
                "driver": "ODBC Driver 17 for SQL Server"
            }
        
        # Ensure remote_database section exists
        if 'remote_database' not in config['database_config']:
            config['database_config']['remote_database'] = {
                "server": "********",
                "database": "VenusHR14",
                "username": "sa",
                "password": "supp0rt@",
                "port": 1888,
                "driver": "ODBC Driver 17 for SQL Server"
            }
        
        return config
        
    except FileNotFoundError:
        logger.error("config.json file not found. Using default configuration.")
        return {
            "google_apps_script": {
                "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
                "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"
            },
            "database_config": {
                "connection_mode": "local",
                "fallback_enabled": True,
                "connection_timeout": 30,
                "local_database": {
                    "server": "localhost",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "windows0819",
                    "port": 1433,
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "remote_database": {
                    "server": "********",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "supp0rt@",
                    "port": 1888,
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            }
        }
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing config.json: {e}. Using default configuration.")
        return {
            "google_apps_script": {
                "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
                "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"
            },
            "database_config": {
                "connection_mode": "local",
                "fallback_enabled": True,
                "connection_timeout": 30,
                "local_database": {
                    "server": "localhost",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "windows0819",
                    "port": 1433,
                    "driver": "ODBC Driver 17 for SQL Server"
                },
                "remote_database": {
                    "server": "********",
                    "database": "VenusHR14",
                    "username": "sa",
                    "password": "supp0rt@",
                    "port": 1888,
                    "driver": "ODBC Driver 17 for SQL Server"
                }
            }
        }

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load configuration
config = load_config()

# Initialize Database Connection Manager
db_manager = DatabaseConnectionManager(config)

# Google Apps Script URLs from config
GOOGLE_APPS_SCRIPT_URL = config["google_apps_script"]["sync_url"]
CHARGE_JOB_DATA_URL = config["google_apps_script"]["charge_job_data_url"]

logger.info(f"Loaded configuration:")
logger.info(f"  - Sync URL: {GOOGLE_APPS_SCRIPT_URL}")
logger.info(f"  - Charge Job Data URL: {CHARGE_JOB_DATA_URL}")
logger.info(f"  - Database Connection Mode: {db_manager.connection_mode}")
logger.info(f"  - Fallback Enabled: {db_manager.fallback_enabled}")

# Add staging configuration
STAGING_CONFIG = config.get("staging_config", {
    "database_table": "staging_attendance",
    "max_records": 10000,
    "auto_cleanup_days": 30,
    "default_mode": "Local Sync Staging"
})

# Test database connections on startup
logger.info("=" * 60)
logger.info("INITIALIZING DATABASE CONNECTION MANAGER")
logger.info("=" * 60)

# Perform initial connection scan with quick timeouts
logger.info("🔍 Performing initial database connection scan...")

def perform_initial_scan():
    """Perform initial database connection scan with quick timeouts."""
    try:
        # For startup, skip local test if we're in remote mode and just test remote
        current_mode = db_manager.connection_mode

        local_success = False
        remote_success = False
        local_message = "Not tested"
        remote_message = "Not tested"

        if current_mode == "remote":
            # If we're in remote mode, test remote first
            logger.info("🧪 Testing remote database connection (timeout: 10s)...")
            try:
                remote_success, remote_message = db_manager.test_connection('remote')
            except Exception as e:
                remote_success, remote_message = False, f"Connection test error: {str(e)}"

            logger.info(f"  📍 Remote database: {'✅ Connected' if remote_success else '❌ Failed'}")
            if not remote_success:
                logger.debug(f"    Remote error: {remote_message}")

            # Only test local if remote fails or if we have time
            if not remote_success:
                logger.info("🧪 Testing local database connection (timeout: 5s)...")
                try:
                    local_success, local_message = db_manager.test_connection('local')
                except Exception as e:
                    local_success, local_message = False, f"Connection test error: {str(e)}"

                logger.info(f"  📍 Local database: {'✅ Connected' if local_success else '❌ Failed'}")
                if not local_success:
                    logger.debug(f"    Local error: {local_message}")
        else:
            # If we're in local mode, test local first
            logger.info("🧪 Testing local database connection (timeout: 5s)...")
            try:
                local_success, local_message = db_manager.test_connection('local')
            except Exception as e:
                local_success, local_message = False, f"Connection test error: {str(e)}"

            logger.info(f"  📍 Local database: {'✅ Connected' if local_success else '❌ Failed'}")
            if not local_success:
                logger.debug(f"    Local error: {local_message}")

            # Test remote as fallback
            logger.info("🧪 Testing remote database connection (timeout: 10s)...")
            try:
                remote_success, remote_message = db_manager.test_connection('remote')
            except Exception as e:
                remote_success, remote_message = False, f"Connection test error: {str(e)}"

            logger.info(f"  📍 Remote database: {'✅ Connected' if remote_success else '❌ Failed'}")
            if not remote_success:
                logger.debug(f"    Remote error: {remote_message}")
        
        # Update connection status
        db_manager.connection_status.update({
            'local': {
                'connected': local_success,
                'last_attempt': datetime.now().isoformat(),
                'error': None if local_success else local_message
            },
            'remote': {
                'connected': remote_success,
                'last_attempt': datetime.now().isoformat(),
                'error': None if remote_success else remote_message
            }
        })
        
        # Report scan results and auto-switch if needed
        if local_success or remote_success:
            logger.info(f"✅ Initial scan complete: {('Local' if local_success else '')} {('Remote' if remote_success else '')} available")

            # Auto-switch to working database if current mode is not working
            current_mode = db_manager.connection_mode
            current_working = (current_mode == "local" and local_success) or (current_mode == "remote" and remote_success)

            if not current_working:
                if current_mode == "local" and not local_success and remote_success:
                    logger.info("🔄 Auto-switching to remote mode (local unavailable)")
                    db_manager.connection_mode = "remote"
                elif current_mode == "remote" and not remote_success and local_success:
                    logger.info("🔄 Auto-switching to local mode (remote unavailable)")
                    db_manager.connection_mode = "local"

            if not local_success and remote_success:
                logger.info("💡 Using remote mode (local unavailable)")
            elif local_success and not remote_success:
                logger.info("💡 Using local mode (remote unavailable)")
        else:
            logger.warning("⚠️ No database connections available at startup")
            logger.info("📝 Connection testing can be triggered through the web interface")
        
        return local_success or remote_success
        
    except Exception as e:
        logger.error(f"❌ Initial scan failed: {str(e)}")
        return False

# Perform the scan
scan_success = perform_initial_scan()

logger.info("✅ Database Connection Manager initialized")
logger.info("🌐 Web interface available for connection management")
logger.info("=" * 60)

def init_staging_database():
    """Initialize the staging database table if it doesn't exist."""
    try:
        # Use the same database as the main application but separate table
        staging_db_path = os.path.join('data', 'staging_attendance.db')
        os.makedirs('data', exist_ok=True)

        conn = sqlite3.connect(staging_db_path)
        cursor = conn.cursor()

        # Create staging table with the same structure as main attendance data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS staging_attendance (
                id TEXT PRIMARY KEY,
                employee_id TEXT NOT NULL,
                employee_name TEXT NOT NULL,
                date TEXT NOT NULL,
                day_of_week TEXT,
                shift TEXT,
                check_in TEXT,
                check_out TEXT,
                regular_hours REAL DEFAULT 0,
                overtime_hours REAL DEFAULT 0,
                total_hours REAL DEFAULT 0,
                task_code TEXT,
                station_code TEXT,
                machine_code TEXT,
                expense_code TEXT,
                raw_charge_job TEXT,
                status TEXT DEFAULT 'staged',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                source_record_id TEXT,
                notes TEXT
            )
        ''')

        # Check if existing columns exist and add them if missing (migration)
        cursor.execute("PRAGMA table_info(staging_attendance)")
        columns = [column[1] for column in cursor.fetchall()]

        # Check for potentially missing columns and add them
        required_columns = {
            'station_code': 'TEXT',
            'task_code': 'TEXT',
            'machine_code': 'TEXT',
            'expense_code': 'TEXT',
            'raw_charge_job': 'TEXT',
            'total_hours': 'REAL DEFAULT 0',
            'leave_type_code': 'TEXT',
            'leave_type_description': 'TEXT',
            'leave_ref_number': 'TEXT',
            'is_alfa': 'BOOLEAN DEFAULT 0',
            'is_on_leave': 'BOOLEAN DEFAULT 0',
            'ptrj_employee_id': 'TEXT DEFAULT "N/A"'
        }

        for col_name, col_type in required_columns.items():
            if col_name not in columns:
                logger.info(f"Adding missing {col_name} column to staging_attendance table")
                cursor.execute(f'ALTER TABLE staging_attendance ADD COLUMN {col_name} {col_type}')
        
        # Create staging operations log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS staging_operations_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                operation_type TEXT NOT NULL,
                table_name TEXT NOT NULL,
                operation_details TEXT,
                affected_record_ids TEXT,
                data_volume INTEGER DEFAULT 0,
                user_identifier TEXT DEFAULT 'system',
                result_status TEXT NOT NULL,
                error_details TEXT,
                query_parameters TEXT,
                ip_address TEXT,
                user_agent TEXT
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_employee_id ON staging_attendance(employee_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_date ON staging_attendance(date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_status ON staging_attendance(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_created_at ON staging_attendance(created_at)')
        
        # Create indexes for log table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_timestamp ON staging_operations_log(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_operation_type ON staging_operations_log(operation_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_log_result_status ON staging_operations_log(result_status)')
        
        conn.commit()
        conn.close()
        
        logger.info("Staging database initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize staging database: {e}")
        return False

def get_staging_db_connection():
    """Get a connection to the staging database."""
    staging_db_path = os.path.join('data', 'staging_attendance.db')
    return sqlite3.connect(staging_db_path)

# Initialize staging database on startup
init_staging_database()

# Staging API Endpoints

@app.route('/api/staging/data', methods=['GET'])
def get_staging_data():
    """Get staging data for display in the grid with optimized employee grouping and enhanced charge job integration."""
    try:
        # Get query parameters for filtering
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_id = request.args.get('employee_id')
        status = request.args.get('status', 'staged')  # Default to only successfully staged records
        limit = int(request.args.get('limit', 1000))
        offset = int(request.args.get('offset', 0))
        optimize_structure = request.args.get('optimize', 'false').lower() == 'true'  # New parameter for optimized structure
        
        # Get user information for logging
        user_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')
        
        # Build query parameters for logging
        query_params = {
            'start_date': start_date,
            'end_date': end_date,
            'employee_id': employee_id,
            'status': status,
            'limit': limit,
            'offset': offset,
            'optimize': optimize_structure
        }
        
        # Fetch employee charge job data from Google Apps Script API
        charge_job_data = {}
        try:
            logger.info("Fetching charge job data from Google Apps Script API for staging data enhancement")
            timeout = config.get("sync_settings", {}).get("timeout_seconds", 15)
            
            response = requests.get(CHARGE_JOB_DATA_URL, timeout=timeout)
            response.raise_for_status()
            
            charge_jobs_response = response.json()
            logger.info(f"Retrieved charge jobs response from Google Apps Script")
            
            # Handle different response formats
            if isinstance(charge_jobs_response, dict):
                if 'data' in charge_jobs_response:
                    employee_list = charge_jobs_response['data']
                elif 'employees' in charge_jobs_response:
                    employee_list = charge_jobs_response['employees']
                else:
                    employee_list = [charge_jobs_response]
            else:
                employee_list = charge_jobs_response
            
            # Process charge job data
            for emp in employee_list:
                emp_name = (emp.get('namaKaryawan') or 
                           emp.get('employeeName') or 
                           emp.get('EmployeeName') or 
                           emp.get('name') or '').strip()
                
                emp_id = (emp.get('employeeId') or 
                         emp.get('EmployeeID') or 
                         emp.get('employeeID') or 
                         emp.get('id') or '').strip()
                
                charge_job = (emp.get('chargeJob') or 
                             emp.get('charge_job') or 
                             emp.get('ChargeJob') or 
                             emp.get('task_code_data') or '').strip()
                
                # Use employee ID as primary key, fallback to name
                employee_key = emp_id if emp_id else emp_name
                if employee_key and charge_job:
                    parsed_data = parse_charge_job_data(charge_job)
                    charge_job_data[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'task_code': parsed_data['task_code'],
                        'station_code': parsed_data['station_code'], 
                        'machine_code': parsed_data['machine_code'],
                        'expense_code': parsed_data['expense_code'],
                        'raw_charge_job': charge_job
                    }
                    
                    # Also index by name for lookup flexibility
                    if emp_name and emp_name != employee_key:
                        charge_job_data[emp_name] = charge_job_data[employee_key]
            
            logger.info(f"Successfully processed {len(charge_job_data)} charge job records")
            
        except Exception as charge_error:
            logger.warning(f"Failed to fetch charge job data from Google Apps Script: {charge_error}")
            # Continue without charge job data
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Build query with filters including leave data and PTRJ Employee ID
        query = '''
            SELECT id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                   check_in, check_out, regular_hours, overtime_hours, total_hours,
                   task_code, station_code, machine_code, expense_code, raw_charge_job,
                   leave_type_code, leave_type_description, leave_ref_number,
                   is_alfa, is_on_leave, status,
                   created_at, updated_at, source_record_id, notes
            FROM staging_attendance
            WHERE 1=1
        '''
        params = []
        
        # Filter for successfully transferred records only (exclude failed transfers)
        if status:
            query += ' AND status = ?'
            params.append(status)
        
        if start_date:
            query += ' AND date >= ?'
            params.append(start_date)
        if end_date:
            query += ' AND date <= ?'
            params.append(end_date)
        if employee_id:
            query += ' AND employee_id = ?'
            params.append(employee_id)
            
        query += ' ORDER BY date ASC, employee_name ASC, created_at DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Get total count for pagination
        count_query = '''
            SELECT COUNT(*) FROM staging_attendance WHERE 1=1
        '''
        count_params = []
        
        if status:
            count_query += ' AND status = ?'
            count_params.append(status)
        if start_date:
            count_query += ' AND date >= ?'
            count_params.append(start_date)
        if end_date:
            count_query += ' AND date <= ?'
            count_params.append(end_date)
        if employee_id:
            count_query += ' AND employee_id = ?'
            count_params.append(employee_id)
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        # Enhance rows with charge job data
        enhanced_rows = []
        for row in rows:
            row_list = list(row)
            emp_id = row[1]
            emp_name = row[2]
            
            # Look up charge job data with enhanced matching logic
            charge_data = None
            
            # Method 1: Exact match by employee ID
            if emp_id and emp_id in charge_job_data:
                charge_data = charge_job_data[emp_id]
                logger.debug(f"Found charge data for {emp_name} by ID: {emp_id}")
            
            # Method 2: Exact match by employee name
            elif emp_name and emp_name in charge_job_data:
                charge_data = charge_job_data[emp_name]
                logger.debug(f"Found charge data for {emp_name} by exact name match")
            
            # Method 3: Case-insensitive match by name
            elif emp_name:
                for charge_key, charge_value in charge_job_data.items():
                    if emp_name.upper() == charge_key.upper():
                        charge_data = charge_value
                        logger.debug(f"Found charge data for {emp_name} by case-insensitive match: {charge_key}")
                        break
            
            # Method 4: Partial name match (for cases like "Doni Saisan" vs "DONI SAISAN" or similar)
            if not charge_data and emp_name:
                for charge_key, charge_value in charge_job_data.items():
                    # Check if the staging name is contained in charge job name or vice versa
                    emp_name_clean = emp_name.upper().strip()
                    charge_key_clean = charge_key.upper().strip()
                    
                    if (emp_name_clean in charge_key_clean or 
                        charge_key_clean in emp_name_clean or
                        # Check for partial matches with at least 70% similarity
                        len(set(emp_name_clean.split()) & set(charge_key_clean.split())) >= 1):
                        charge_data = charge_value
                        logger.debug(f"Found charge data for {emp_name} by partial match: {charge_key}")
                        break
            
            # Method 5: Default fallback - use first available charge job data if no specific match found
            # This ensures that staging records get some charge job data populated
            if not charge_data and charge_job_data:
                # Use a generic charge job entry (prioritize common ones)
                common_entries = [k for k in charge_job_data.keys() if 'LABOUR' in charge_job_data[k].get('expense_code', '').upper()]
                if common_entries:
                    charge_data = charge_job_data[common_entries[0]]
                    logger.debug(f"Using fallback charge data for {emp_name}: {common_entries[0]}")
                else:
                    # Use first available entry
                    first_key = next(iter(charge_job_data))
                    charge_data = charge_job_data[first_key]
                    logger.debug(f"Using first available charge data for {emp_name}: {first_key}")
            
            # Update charge job fields if we have the data
            if charge_data:
                # Update fields: task_code, station_code, machine_code, expense_code, raw_charge_job
                # Note: Column positions shifted by 1 due to ptrj_employee_id addition at position 3
                row_list[12] = charge_data['task_code'] or row_list[12]  # task_code
                row_list[13] = charge_data['station_code'] or row_list[13]  # station_code
                row_list[14] = charge_data['machine_code'] or row_list[14]  # machine_code
                row_list[15] = charge_data['expense_code'] or row_list[15]  # expense_code
                row_list[16] = charge_data['raw_charge_job'] or row_list[16]  # raw_charge_job
                
                logger.debug(f"Enhanced {emp_name} with charge data: machine={charge_data['machine_code']}, expense={charge_data['expense_code']}")
            else:
                logger.warning(f"No charge job data found for {emp_name} (ID: {emp_id})")
            
            enhanced_rows.append(tuple(row_list))
        
        # Format data for JSON response
        if optimize_structure:
            # Group by employee for optimized structure
            employees_data = {}
            
            for row in enhanced_rows:
                emp_id = row[1]
                emp_name = row[2]
                ptrj_emp_id = row[3]
                employee_key = f"{emp_id}_{emp_name}"
                
                # Create employee entry if not exists
                if employee_key not in employees_data:
                    employees_data[employee_key] = {
                        'identitas_karyawan': {
                            'employee_id_venus': emp_id,
                            'employee_id_ptrj': ptrj_emp_id,
                            'employee_name': emp_name,
                            'task_code': row[12],
                            'station_code': row[13],
                            'machine_code': row[14],
                            'expense_code': row[15],
                            'raw_charge_job': row[16]
                        },
                        'data_presensi': []
                    }
                
                # Add attendance record
                attendance_record = {
                    'id': row[0],
                    'date': row[4],
                    'day_of_week': row[5],
                    'shift': row[6],
                    'check_in': row[7],
                    'check_out': row[8],
                    'regular_hours': round(float(row[9] or 0), 2),
                    'overtime_hours': round(float(row[10] or 0), 2),
                    'total_hours': round(float(row[11] or 0), 2),
                    'leave_type_code': row[17],
                    'leave_type_description': row[18],
                    'leave_ref_number': row[19],
                    'is_alfa': bool(row[20]),
                    'is_on_leave': bool(row[21]),
                    'status': row[22],
                    'created_at': row[23],
                    'updated_at': row[24],
                    'source_record_id': row[25],
                    'notes': row[26],
                    'transfer_status': 'success' if row[22] == 'staged' else 'pending'
                }
                
                employees_data[employee_key]['data_presensi'].append(attendance_record)
            
            # Convert to list format
            formatted_data = list(employees_data.values())
            
            # Calculate totals for optimized response
            total_employees = len(formatted_data)
            total_attendance_records = sum(len(emp['data_presensi']) for emp in formatted_data)
            successful_transfers = sum(
                len([rec for rec in emp['data_presensi'] if rec['transfer_status'] == 'success'])
                for emp in formatted_data
            )
            
        else:
            # Standard flat structure (backward compatibility)
            formatted_data = []
            for row in enhanced_rows:
                formatted_record = {
                    'id': row[0],
                    'employee_id': row[1],
                    'employee_name': row[2],
                    'ptrj_employee_id': row[3],
                    'date': row[4],
                    'day_of_week': row[5],
                    'shift': row[6],
                    'check_in': row[7],
                    'check_out': row[8],
                    'regular_hours': round(float(row[9] or 0), 2),
                    'overtime_hours': round(float(row[10] or 0), 2),
                    'total_hours': round(float(row[11] or 0), 2),
                    'task_code': row[12],
                    'station_code': row[13],
                    'machine_code': row[14],
                    'expense_code': row[15],
                    'raw_charge_job': row[16],
                    'leave_type_code': row[17],
                    'leave_type_description': row[18],
                    'leave_ref_number': row[19],
                    'is_alfa': bool(row[20]),
                    'is_on_leave': bool(row[21]),
                    'status': row[22],
                    'created_at': row[23],
                    'updated_at': row[24],
                    'source_record_id': row[25],
                    'notes': row[26],
                    'transfer_status': 'success' if row[22] == 'staged' else 'pending'
                }
                formatted_data.append(formatted_record)
            
            total_employees = len(set(row[1] for row in enhanced_rows))  # Unique employee count
            total_attendance_records = len(formatted_data)
            successful_transfers = len([r for r in formatted_data if r['transfer_status'] == 'success'])
        
        # Log the read operation
        log_staging_operation(
            operation_type='READ',
            table_name='staging_attendance',
            operation_details=f'Retrieved staging data with filters: status={status}, optimize={optimize_structure}, enhanced with charge job data',
            data_volume=total_attendance_records,
            result_status='success',
            query_parameters=query_params,
            user_ip=user_ip,
            user_agent=user_agent
        )
        
        response_data = {
            'success': True,
            'data': formatted_data,
            'total_records': total_count,
            'returned_records': total_attendance_records,
            'total_employees': total_employees,
            'successfully_transferred': successful_transfers,
            'structure_optimized': optimize_structure,
            'charge_job_enhancement': {
                'enabled': len(charge_job_data) > 0,
                'records_enhanced': len(charge_job_data),
                'source': 'Google Apps Script API'
            },
            'pagination': {
                'limit': limit,
                'offset': offset,
                'has_more': (offset + total_attendance_records) < total_count
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting staging data: {str(e)}")
        
        # Log the failed operation
        try:
            log_staging_operation(
                operation_type='READ',
                table_name='staging_attendance',
                operation_details='Failed to retrieve staging data',
                result_status='failure',
                error_details=str(e),
                user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
                user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
            )
        except:
            pass
        
        return jsonify({
            'success': False,
            'error': f'Failed to retrieve staging data: {str(e)}'
        }), 500

@app.route('/api/staging/data-grouped', methods=['GET'])
def get_staging_data_grouped():
    """Get staging data with new optimized structure by default - groups by employee with comprehensive data."""
    try:
        # Get query parameters for filtering
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_id = request.args.get('employee_id')
        status = request.args.get('status', 'staged')  # Default to only successfully staged records
        limit = int(request.args.get('limit', 1000))
        offset = int(request.args.get('offset', 0))
        
        # Get user information for logging
        user_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')
        
        # Build query parameters for logging
        query_params = {
            'start_date': start_date,
            'end_date': end_date,
            'employee_id': employee_id,
            'status': status,
            'limit': limit,
            'offset': offset
        }
        
        # Fetch employee charge job data from Google Apps Script API
        charge_job_data = {}
        try:
            logger.info("Fetching charge job data from Google Apps Script API for grouped staging data")
            timeout = config.get("sync_settings", {}).get("timeout_seconds", 15)
            
            response = requests.get(CHARGE_JOB_DATA_URL, timeout=timeout)
            response.raise_for_status()
            
            charge_jobs_response = response.json()
            logger.info(f"Retrieved charge jobs response from Google Apps Script")
            
            # Handle different response formats
            if isinstance(charge_jobs_response, dict):
                if 'data' in charge_jobs_response:
                    employee_list = charge_jobs_response['data']
                elif 'employees' in charge_jobs_response:
                    employee_list = charge_jobs_response['employees']
                else:
                    employee_list = [charge_jobs_response]
            else:
                employee_list = charge_jobs_response
            
            # Process charge job data
            for emp in employee_list:
                emp_name = (emp.get('namaKaryawan') or 
                           emp.get('employeeName') or 
                           emp.get('EmployeeName') or 
                           emp.get('name') or '').strip()
                
                emp_id = (emp.get('employeeId') or 
                         emp.get('EmployeeID') or 
                         emp.get('employeeID') or 
                         emp.get('id') or '').strip()
                
                charge_job = (emp.get('chargeJob') or 
                             emp.get('charge_job') or 
                             emp.get('ChargeJob') or 
                             emp.get('task_code_data') or '').strip()
                
                # Use employee ID as primary key, fallback to name
                employee_key = emp_id if emp_id else emp_name
                if employee_key and charge_job:
                    parsed_data = parse_charge_job_data(charge_job)
                    charge_job_data[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'task_code': parsed_data['task_code'],
                        'station_code': parsed_data['station_code'], 
                        'machine_code': parsed_data['machine_code'],
                        'expense_code': parsed_data['expense_code'],
                        'raw_charge_job': charge_job
                    }
                    
                    # Also index by name for lookup flexibility
                    if emp_name and emp_name != employee_key:
                        charge_job_data[emp_name] = charge_job_data[employee_key]
            
            logger.info(f"Successfully processed {len(charge_job_data)} charge job records")
            
        except Exception as charge_error:
            logger.warning(f"Failed to fetch charge job data from Google Apps Script: {charge_error}")
            # Continue without charge job data
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Build query with filters including leave data and PTRJ Employee ID
        query = '''
            SELECT id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                   check_in, check_out, regular_hours, overtime_hours, total_hours,
                   task_code, station_code, machine_code, expense_code, raw_charge_job,
                   leave_type_code, leave_type_description, leave_ref_number,
                   is_alfa, is_on_leave, status,
                   created_at, updated_at, source_record_id, notes
            FROM staging_attendance
            WHERE 1=1
        '''
        params = []
        
        # Filter for successfully transferred records only (exclude failed transfers)
        if status:
            query += ' AND status = ?'
            params.append(status)
        
        if start_date:
            query += ' AND date >= ?'
            params.append(start_date)
        if end_date:
            query += ' AND date <= ?'
            params.append(end_date)
        if employee_id:
            query += ' AND employee_id = ?'
            params.append(employee_id)
            
        query += ' ORDER BY date ASC, employee_name ASC, created_at DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Get total count for pagination
        count_query = '''
            SELECT COUNT(*) FROM staging_attendance WHERE 1=1
        '''
        count_params = []
        
        if status:
            count_query += ' AND status = ?'
            count_params.append(status)
        if start_date:
            count_query += ' AND date >= ?'
            count_params.append(start_date)
        if end_date:
            count_query += ' AND date <= ?'
            count_params.append(end_date)
        if employee_id:
            count_query += ' AND employee_id = ?'
            count_params.append(employee_id)
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        # Enhance rows with charge job data
        enhanced_rows = []
        for row in rows:
            row_list = list(row)
            emp_id = row[1]
            emp_name = row[2]
            
            # Look up charge job data with enhanced matching logic
            charge_data = None
            
            # Method 1: Exact match by employee ID
            if emp_id and emp_id in charge_job_data:
                charge_data = charge_job_data[emp_id]
                logger.debug(f"Found charge data for {emp_name} by ID: {emp_id}")
            
            # Method 2: Exact match by employee name
            elif emp_name and emp_name in charge_job_data:
                charge_data = charge_job_data[emp_name]
                logger.debug(f"Found charge data for {emp_name} by exact name match")
            
            # Method 3: Case-insensitive match by name
            elif emp_name:
                for charge_key, charge_value in charge_job_data.items():
                    if emp_name.upper() == charge_key.upper():
                        charge_data = charge_value
                        logger.debug(f"Found charge data for {emp_name} by case-insensitive match: {charge_key}")
                        break
            
            # Method 4: Partial name match
            if not charge_data and emp_name:
                for charge_key, charge_value in charge_job_data.items():
                    emp_name_clean = emp_name.upper().strip()
                    charge_key_clean = charge_key.upper().strip()
                    
                    if (emp_name_clean in charge_key_clean or 
                        charge_key_clean in emp_name_clean or
                        len(set(emp_name_clean.split()) & set(charge_key_clean.split())) >= 1):
                        charge_data = charge_value
                        logger.debug(f"Found charge data for {emp_name} by partial match: {charge_key}")
                        break
            
            # Update charge job fields if we have the data
            if charge_data:
                row_list[12] = charge_data['task_code'] or row_list[12]  # task_code
                row_list[13] = charge_data['station_code'] or row_list[13]  # station_code
                row_list[14] = charge_data['machine_code'] or row_list[14]  # machine_code
                row_list[15] = charge_data['expense_code'] or row_list[15]  # expense_code
                row_list[16] = charge_data['raw_charge_job'] or row_list[16]  # raw_charge_job
                
                logger.debug(f"Enhanced {emp_name} with charge data: machine={charge_data['machine_code']}, expense={charge_data['expense_code']}")
            else:
                logger.warning(f"No charge job data found for {emp_name} (ID: {emp_id})")
            
            enhanced_rows.append(tuple(row_list))
        
        # Group by employee for optimized structure (new default format)
        employees_data = {}
        
        for row in enhanced_rows:
            emp_id = row[1]
            emp_name = row[2]
            ptrj_emp_id = row[3]
            employee_key = f"{emp_id}_{emp_name}"
            
            # Create employee entry if not exists
            if employee_key not in employees_data:
                employees_data[employee_key] = {
                    'identitas_karyawan': {
                        'employee_id_venus': emp_id,
                        'employee_id_ptrj': ptrj_emp_id,
                        'employee_name': emp_name,
                        'task_code': row[12],
                        'station_code': row[13],
                        'machine_code': row[14],
                        'expense_code': row[15],
                        'raw_charge_job': row[16]
                    },
                    'data_presensi': []
                }
            
            # Add attendance record
            attendance_record = {
                'id': row[0],
                'date': row[4],
                'day_of_week': row[5],
                'shift': row[6],
                'check_in': row[7],
                'check_out': row[8],
                'regular_hours': round(float(row[9] or 0), 2),
                'overtime_hours': round(float(row[10] or 0), 2),
                'total_hours': round(float(row[11] or 0), 2),
                'leave_type_code': row[17],
                'leave_type_description': row[18],
                'leave_ref_number': row[19],
                'is_alfa': bool(row[20]),
                'is_on_leave': bool(row[21]),
                'status': row[22],
                'created_at': row[23],
                'updated_at': row[24],
                'source_record_id': row[25],
                'notes': row[26],
                'transfer_status': 'success' if row[22] == 'staged' else 'pending'
            }
            
            employees_data[employee_key]['data_presensi'].append(attendance_record)
        
        # Convert to list format
        formatted_data = list(employees_data.values())
        
        # Calculate totals
        total_employees = len(formatted_data)
        total_attendance_records = sum(len(emp['data_presensi']) for emp in formatted_data)
        successful_transfers = sum(
            len([rec for rec in emp['data_presensi'] if rec['transfer_status'] == 'success'])
            for emp in formatted_data
        )
        
        # Log the read operation
        log_staging_operation(
            operation_type='READ',
            table_name='staging_attendance',
            operation_details=f'Retrieved grouped staging data with filters: status={status}, enhanced with charge job data and PTRJ Employee ID',
            data_volume=total_attendance_records,
            result_status='success',
            query_parameters=query_params,
            user_ip=user_ip,
            user_agent=user_agent
        )
        
        response_data = {
            'success': True,
            'data': formatted_data,
            'total_records': total_count,
            'returned_records': total_attendance_records,
            'total_employees': total_employees,
            'successfully_transferred': successful_transfers,
            'structure_type': 'grouped_by_employee',
            'charge_job_enhancement': {
                'enabled': len(charge_job_data) > 0,
                'records_enhanced': len(charge_job_data),
                'source': 'Google Apps Script API'
            },
            'ptrj_integration': {
                'enabled': True,
                'description': 'PTRJ Employee ID included in identitas_karyawan'
            },
            'pagination': {
                'limit': limit,
                'offset': offset,
                'has_more': (offset + total_attendance_records) < total_count
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting grouped staging data: {str(e)}")
        
        # Log the failed operation
        try:
            log_staging_operation(
                operation_type='READ',
                table_name='staging_attendance',
                operation_details='Failed to retrieve grouped staging data',
                result_status='failure',
                error_details=str(e),
                user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
                user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
            )
        except:
            pass
        
        return jsonify({
            'success': False,
            'error': f'Failed to retrieve grouped staging data: {str(e)}'
        }), 500

@app.route('/api/staging/data', methods=['POST'])
def add_staging_data():
    """Add new staging data."""
    try:
        # Enhanced debugging for 400 errors
        logger.info(f"Staging data request received. Content-Type: {request.headers.get('Content-Type')}")
        logger.info(f"Request data type: {type(request.data)}")
        logger.info(f"Request data length: {len(request.data) if request.data else 0}")
        
        # Try to get data from request
        data = None
        try:
            data = request.get_json(force=True)  # Force JSON parsing
            logger.info(f"Successfully parsed JSON data. Type: {type(data)}")
            if data:
                logger.info(f"Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        except Exception as json_error:
            logger.error(f"JSON parsing error: {json_error}")
            logger.error(f"Raw request data: {request.data.decode('utf-8')[:500] if request.data else 'No data'}")
            return jsonify({
                'success': False,
                'error': f'Invalid JSON format: {str(json_error)}',
                'debug_info': {
                    'content_type': request.headers.get('Content-Type'),
                    'data_length': len(request.data) if request.data else 0,
                    'raw_data_preview': request.data.decode('utf-8')[:200] if request.data else 'No data'
                }
            }), 400
        
        if not data or 'records' not in data:
            logger.error(f"No records provided. Data: {data}")
            return jsonify({
                'success': False,
                'error': 'No records provided',
                'debug_info': {
                    'received_data': data,
                    'expected_format': {'records': [{'employee_id': '...', 'employee_name': '...', 'date': '...'}]}
                }
            }), 400
        
        records = data['records']
        if not isinstance(records, list):
            logger.error(f"Records is not a list. Type: {type(records)}, Value: {records}")
            return jsonify({
                'success': False,
                'error': 'Records must be a list',
                'debug_info': {
                    'records_type': str(type(records)),
                    'records_value': str(records)[:200]
                }
            }), 400
        
        if len(records) == 0:
            logger.error("Empty records list provided")
            return jsonify({
                'success': False,
                'error': 'Records list is empty'
            }), 400
        
        logger.info(f"Processing {len(records)} staging records")
        
        # Validate first record structure for debugging
        if records and len(records) > 0:
            first_record = records[0]
            logger.info(f"First record keys: {list(first_record.keys()) if isinstance(first_record, dict) else 'Not a dict'}")
            logger.info(f"First record sample: {str(first_record)[:200]}")
            
            # Validate required fields
            required_fields = ['employee_id', 'employee_name', 'date']
            missing_fields = [field for field in required_fields if field not in first_record]
            if missing_fields:
                logger.error(f"Missing required fields in first record: {missing_fields}")
                return jsonify({
                    'success': False,
                    'error': f'Missing required fields: {missing_fields}',
                    'debug_info': {
                        'first_record': first_record,
                        'required_fields': required_fields,
                        'missing_fields': missing_fields
                    }
            }), 400
        
        # Get user information for logging
        user_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        added_records = 0
        record_ids = []
        error_details = []
        
        for i, record in enumerate(records):
            try:
                # Validate record structure
                if not isinstance(record, dict):
                    error_msg = f"Record {i+1} is not a dictionary: {type(record)}"
                    logger.error(error_msg)
                    error_details.append(error_msg)
                    continue
                
                # Generate UUID for record ID
                record_id = str(uuid.uuid4())
                record_ids.append(record_id)
                
                # Calculate total hours with error handling
                try:
                    regular_hours = float(record.get('regular_hours', 0))
                    overtime_hours = float(record.get('overtime_hours', 0))
                    total_hours = regular_hours + overtime_hours
                except (ValueError, TypeError) as hours_error:
                    error_msg = f"Record {i+1}: Invalid hours data - regular: {record.get('regular_hours')}, overtime: {record.get('overtime_hours')}"
                    logger.error(error_msg)
                    error_details.append(error_msg)
                    # Use default values
                    regular_hours = 0
                    overtime_hours = 0
                    total_hours = 0
                
                cursor.execute('''
                    INSERT INTO staging_attendance (
                        id, employee_id, employee_name, date, day_of_week, shift,
                        check_in, check_out, regular_hours, overtime_hours, total_hours,
                        task_code, station_code, machine_code, expense_code, raw_charge_job, status,
                        source_record_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record_id,
                    record.get('employee_id', ''),
                    record.get('employee_name', ''),
                    record.get('date', ''),
                    record.get('day_of_week', ''),
                    record.get('shift', 'Regular'),
                    record.get('check_in', ''),
                    record.get('check_out', ''),
                    regular_hours,
                    overtime_hours,
                    total_hours,
                    record.get('task_code', ''),
                    record.get('station_code', ''),
                    record.get('machine_code', ''),
                    record.get('expense_code', ''),
                    record.get('raw_charge_job', ''),
                    'staged',  # Default status
                    record.get('source_record_id', ''),
                    record.get('notes', '')
                ))
                
                added_records += 1
                
            except Exception as record_error:
                error_msg = f"Failed to add record for {record.get('employee_name', 'unknown')}: {str(record_error)}"
                error_details.append(error_msg)
                logger.error(error_msg)
        
        conn.commit()
        conn.close()
        
        # Log the write operation
        operation_result = 'success' if added_records > 0 else 'failure'
        error_summary = '; '.join(error_details[:3]) if error_details else None  # Limit error details
        
        log_staging_operation(
            operation_type='BULK_INSERT',
            table_name='staging_attendance',
            operation_details=f'Added {added_records} staging records from {len(records)} submitted',
            affected_record_ids=record_ids,
            data_volume=added_records,
            result_status=operation_result,
            error_details=error_summary,
            user_ip=user_ip,
            user_agent=user_agent
        )
        
        if added_records == 0:
            return jsonify({
                'success': False,
                'error': 'No records were added',
                'errors': error_details
            }), 400
        
        return jsonify({
            'success': True,
            'message': f'Successfully added {added_records} staging records',
            'added_records': added_records,
            'total_submitted': len(records),
            'errors': error_details if error_details else None
        })
        
    except Exception as e:
        logger.error(f"Error adding staging data: {str(e)}")
        
        # Log the failed operation
        try:
            log_staging_operation(
                operation_type='BULK_INSERT',
                table_name='staging_attendance',
                operation_details='Failed to add staging records',
                data_volume=0,
                result_status='failure',
                error_details=str(e),
                user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
                user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
            )
        except:
            pass
        
        return jsonify({
            'success': False,
            'error': f'Failed to add staging data: {str(e)}'
        }), 500

@app.route('/api/staging/data/<staging_id>', methods=['PUT'])
def update_staging_record(staging_id):
    """Update an existing staging record."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute('SELECT id FROM staging_attendance WHERE id = ?', (staging_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Staging record not found'
            }), 404
        
        # Build update query dynamically based on provided fields
        update_fields = []
        params = []
        
        updatable_fields = [
            'employee_id', 'employee_name', 'date', 'day_of_week', 'shift',
            'check_in', 'check_out', 'regular_hours', 'overtime_hours',
            'task_code', 'station_code', 'machine_code', 'expense_code', 'raw_charge_job',
            'status', 'notes'
        ]
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f'{field} = ?')
                params.append(data[field])
        
        if update_fields:
            # Always update the updated_at timestamp
            update_fields.append('updated_at = CURRENT_TIMESTAMP')
            
            # Recalculate total hours if regular or overtime hours changed
            if 'regular_hours' in data or 'overtime_hours' in data:
                # Get current values if not provided
                cursor.execute('SELECT regular_hours, overtime_hours FROM staging_attendance WHERE id = ?', (staging_id,))
                current_values = cursor.fetchone()
                
                regular_hours = data.get('regular_hours', current_values[0])
                overtime_hours = data.get('overtime_hours', current_values[1])
                total_hours = float(regular_hours) + float(overtime_hours)
                
                update_fields.append('total_hours = ?')
                params.append(total_hours)
            
            query = f'UPDATE staging_attendance SET {", ".join(update_fields)} WHERE id = ?'
            params.append(staging_id)
            
            cursor.execute(query, params)
            conn.commit()
        
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Staging record updated successfully',
            'record_id': staging_id
        })
        
    except Exception as e:
        logger.error(f"Error updating staging record: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to update staging record: {str(e)}'
        }), 500

@app.route('/api/staging/data/<staging_id>', methods=['DELETE'])
def delete_staging_record(staging_id):
    """Remove a record from staging."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute('SELECT id FROM staging_attendance WHERE id = ?', (staging_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Staging record not found'
            }), 404
        
        # Delete the record
        cursor.execute('DELETE FROM staging_attendance WHERE id = ?', (staging_id,))
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Staging record deleted successfully',
            'record_id': staging_id
        })
        
    except Exception as e:
        logger.error(f"Error deleting staging record: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to delete staging record: {str(e)}'
        }), 500

@app.route('/api/staging/upload', methods=['POST'])
def prepare_staging_upload():
    """Prepare staging data for database upload (future implementation)."""
    try:
        data = request.get_json()
        record_ids = data.get('record_ids', []) if data else []
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        if record_ids:
            # Update specific records status to 'ready_for_upload'
            placeholders = ','.join(['?' for _ in record_ids])
            cursor.execute(f'''
                UPDATE staging_attendance 
                SET status = 'ready_for_upload', updated_at = CURRENT_TIMESTAMP 
                WHERE id IN ({placeholders})
            ''', record_ids)
        else:
            # Update all staged records
            cursor.execute('''
                UPDATE staging_attendance 
                SET status = 'ready_for_upload', updated_at = CURRENT_TIMESTAMP 
                WHERE status = 'staged'
            ''')
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'Prepared {affected_rows} records for upload',
            'affected_records': affected_rows,
            'status': 'ready_for_upload'
        })
        
    except Exception as e:
        logger.error(f"Error preparing staging upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to prepare staging upload: {str(e)}'
        }), 500

@app.route('/api/staging/stats', methods=['GET'])
def get_staging_stats():
    """Get staging statistics."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Get counts by status
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM staging_attendance 
            GROUP BY status
        ''')
        status_counts = dict(cursor.fetchall())
        
        # Get total records
        cursor.execute('SELECT COUNT(*) FROM staging_attendance')
        total_records = cursor.fetchone()[0]
        
        # Get date range
        cursor.execute('SELECT MIN(date), MAX(date) FROM staging_attendance')
        date_range = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_records': total_records,
                'status_counts': status_counts,
                'date_range': {
                    'earliest': date_range[0],
                    'latest': date_range[1]
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting staging stats: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get staging stats: {str(e)}'
        }), 500

@app.route('/api/staging/delete-all', methods=['DELETE'])
def delete_all_staging_data():
    """Delete all staging data (complete purge)."""
    try:
        # Get user information for logging
        user_ip = request.environ.get('REMOTE_ADDR', 'unknown')
        user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Get count before deletion for logging
        cursor.execute('SELECT COUNT(*) FROM staging_attendance')
        records_before = cursor.fetchone()[0]
        
        if records_before == 0:
            conn.close()
            return jsonify({
                'success': True,
                'message': 'No staging data to delete',
                'deleted_records': 0
            })
        
        # Delete all staging records
        cursor.execute('DELETE FROM staging_attendance')
        deleted_count = cursor.rowcount
        
        # Reset the staging operations log (optional - keep history or clear it too)
        # cursor.execute('DELETE FROM staging_operations_log')
        
        conn.commit()
        conn.close()
        
        # Log the operation
        log_staging_operation(
            operation_type='DELETE_ALL',
            table_name='staging_attendance',
            operation_details=f'Complete purge of staging database - deleted {deleted_count} records',
            data_volume=deleted_count,
            result_status='success',
            user_ip=user_ip,
            user_agent=user_agent
        )
        
        logger.info(f"✅ Successfully deleted all {deleted_count} staging records")
        
        return jsonify({
            'success': True,
            'message': f'Successfully deleted all staging data',
            'deleted_records': deleted_count,
            'records_before': records_before
        })
        
    except Exception as e:
        logger.error(f"Error deleting all staging data: {str(e)}")
        
        # Log the failed operation
        try:
            log_staging_operation(
                operation_type='DELETE_ALL',
                table_name='staging_attendance',
                operation_details='Failed to delete all staging records',
                result_status='failure',
                error_details=str(e),
                user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
                user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
            )
        except:
            pass
        
        return jsonify({
            'success': False,
            'error': f'Failed to delete all staging data: {str(e)}'
        }), 500

@app.route('/api/staging/employees', methods=['GET'])
def get_staging_employees():
    """Get list of employees available for staging selection."""
    try:
        if reporter is None:
            return jsonify({
                'success': False,
                'error': 'Database connection not available',
                'employees': []
            }), 500
        
        # Get employees list from the database
        employees = reporter.get_employees_list()
        
        # Format for multi-select dropdown
        formatted_employees = []
        for emp in employees:
            formatted_employees.append({
                'employee_id': emp.get('EmployeeID', ''),
                'employee_name': emp.get('EmployeeName', ''),
                'display_name': f"{emp.get('EmployeeID', '')} - {emp.get('EmployeeName', '')}",
                'value': emp.get('EmployeeID', '')  # Use employee ID as value
            })
        
        # Sort by employee name for better UX
        formatted_employees.sort(key=lambda x: x['employee_name'])
        
        return jsonify({
            'success': True,
            'employees': formatted_employees,
            'total_employees': len(formatted_employees)
        })
        
    except Exception as e:
        logger.error(f"Error getting employees for staging: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get employees: {str(e)}',
            'employees': []
        }), 500

@app.route('/api/staging/selective-copy', methods=['POST'])
def selective_copy_to_staging():
    """Copy attendance data for selected employees within specified date range to staging."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Get parameters
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        employee_ids = data.get('employee_ids', [])  # List of selected employee IDs
        bus_code = data.get('bus_code', 'PTRJ')
        
        # Validation
        if not start_date or not end_date:
            return jsonify({
                'success': False,
                'error': 'start_date and end_date are required'
            }), 400
        
        if not employee_ids or not isinstance(employee_ids, list):
            return jsonify({
                'success': False,
                'error': 'employee_ids must be provided as a non-empty list'
            }), 400
        
        if len(employee_ids) == 0:
            return jsonify({
                'success': False,
                'error': 'At least one employee must be selected'
            }), 400
        
        logger.info(f"Selective copy to staging: {len(employee_ids)} employees, {start_date} to {end_date}")
        
        # Get data availability information to filter out unavailable dates
        from datetime import datetime
        
        # Get latest available data date
        latest_available_date = reporter.get_latest_available_data_date()
        
        # Get attendance data from main system using enhanced reporter
        attendance_data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter by selected employee IDs
        attendance_data = [record for record in attendance_data if record.get('EmployeeID') in employee_ids]
        
        # Filter out records with dates beyond the latest available data
        if latest_available_date:
            original_count = len(attendance_data)
            attendance_data = [
                record for record in attendance_data 
                if reporter.is_date_data_available(
                    record.get('TADate').date() if hasattr(record.get('TADate'), 'date') else record.get('TADate'),
                    latest_available_date
                )
            ]
            filtered_count = len(attendance_data)
            if original_count > filtered_count:
                logger.info(f"Filtered out {original_count - filtered_count} records with unavailable dates (beyond {latest_available_date})")
        
        if not attendance_data:
            return jsonify({
                'success': False,
                'error': f'No attendance records found for selected employees ({len(employee_ids)} employees) in date range {start_date} to {end_date}'
            }), 404
        
        # Get PTRJ Employee ID mapping
        ptrj_mapping = {}
        try:
            ptrj_mapping = reporter.get_ptrj_employee_mapping()
            logger.info(f"Retrieved PTRJ mapping for {len(ptrj_mapping)} entries")
        except Exception as e:
            logger.warning(f"Could not retrieve PTRJ mapping: {e}")
        
        # Convert attendance data to staging format
        staging_records = []
        for record in attendance_data:
            employee_name = record.get('EmployeeName', '')
            
            # Get PTRJ Employee ID for this employee
            ptrj_employee_id = reporter._match_ptrj_employee_id(employee_name, ptrj_mapping) if hasattr(reporter, '_match_ptrj_employee_id') else "N/A"
            
            # Handle date formatting - TADate might be string or datetime
            ta_date = record.get('TADate')
            if ta_date:
                if hasattr(ta_date, 'strftime'):
                    date_str = ta_date.strftime('%Y-%m-%d')
                    source_id_date = ta_date.strftime('%Y%m%d')
                else:
                    # TADate is already a string
                    date_str = str(ta_date)[:10]  # Get YYYY-MM-DD part
                    source_id_date = str(ta_date).replace('-', '')[:8]  # Get YYYYMMDD
            else:
                date_str = ''
                source_id_date = ''
            
            # Handle time formatting - might be string or datetime
            check_in = record.get('TACheckIn')
            check_in_str = ''
            if check_in:
                if hasattr(check_in, 'strftime'):
                    check_in_str = check_in.strftime('%H:%M')
                else:
                    check_in_str = str(check_in)[:5]  # Get HH:MM part
            
            check_out = record.get('TACheckOut')
            check_out_str = ''
            if check_out:
                if hasattr(check_out, 'strftime'):
                    check_out_str = check_out.strftime('%H:%M')
                else:
                    check_out_str = str(check_out)[:5]  # Get HH:MM part
            
            staging_record = {
                'employee_id': record.get('EmployeeID', ''),
                'employee_name': employee_name,
                'ptrj_employee_id': ptrj_employee_id,
                'date': date_str,
                'day_of_week': record.get('DayOfWeek', ''),
                'shift': record.get('Shift', ''),
                'check_in': check_in_str,
                'check_out': check_out_str,
                'regular_hours': float(record.get('RegularHours', 0)),
                'overtime_hours': float(record.get('OvertimeHours', 0)),
                'task_code': '',  # To be filled from charge job data
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'raw_charge_job': '',  # To be filled from charge job data
                'source_record_id': f"{record.get('EmployeeID', '')}_{source_id_date}",
                'notes': f'Selective copy for {len(employee_ids)} employees from {start_date} to {end_date}'
            }
            staging_records.append(staging_record)
        
        # Add records to staging database
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        added_records = []
        errors = []
        
        for record in staging_records:
            try:
                staging_id = str(uuid.uuid4())
                
                # Extract leave data if available
                leave_data = record.get('leave_data')
                leave_type_code = leave_data.get('leave_type_code') if leave_data else None
                leave_type_description = leave_data.get('leave_type_description') if leave_data else None
                leave_ref_number = leave_data.get('ref_number') if leave_data else None
                is_alfa = record.get('status') == 'alfa'
                is_on_leave = record.get('status') == 'on_leave'

                cursor.execute('''
                    INSERT INTO staging_attendance (
                        id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                        check_in, check_out, regular_hours, overtime_hours, total_hours,
                        task_code, station_code, machine_code, expense_code, raw_charge_job,
                        leave_type_code, leave_type_description, leave_ref_number,
                        is_alfa, is_on_leave, status, source_record_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    staging_id, record['employee_id'], record['employee_name'], record['ptrj_employee_id'],
                    record['date'], record['day_of_week'], record['shift'],
                    record['check_in'], record['check_out'],
                    record['regular_hours'], record['overtime_hours'],
                    record['regular_hours'] + record['overtime_hours'],
                    record['task_code'], record['station_code'],
                    record['machine_code'], record['expense_code'], record['raw_charge_job'],
                    leave_type_code, leave_type_description, leave_ref_number,
                    is_alfa, is_on_leave, 'staged',  # Default status
                    record['source_record_id'], record['notes']
                ))
                
                added_records.append(staging_id)
                
            except Exception as record_error:
                errors.append({
                    'employee_id': record.get('employee_id', 'unknown'),
                    'employee_name': record.get('employee_name', 'unknown'),
                    'date': record.get('date', 'unknown'),
                    'error': str(record_error)
                })
                continue
        
        conn.commit()
        conn.close()
        
        # Log the operation
        log_staging_operation(
            operation_type='SELECTIVE_COPY',
            table_name='staging_attendance',
            operation_details=f'Selective copy: {len(employee_ids)} employees, {start_date} to {end_date}, {len(added_records)} records copied',
            affected_record_ids=added_records[:100],  # Limit for logging
            data_volume=len(added_records),
            result_status='success' if len(added_records) > 0 else 'partial',
            error_details=f'{len(errors)} errors' if errors else None,
            query_parameters={
                'start_date': start_date,
                'end_date': end_date,
                'employee_ids': employee_ids,
                'bus_code': bus_code
            },
            user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
            user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
        )
        
        success_message = f'Successfully copied {len(added_records)} records to staging for {len(employee_ids)} employees'
        if errors:
            success_message += f' ({len(errors)} errors occurred)'
        
        return jsonify({
            'success': True,
            'message': success_message,
            'copied_records': len(added_records),
            'selected_employees': len(employee_ids),
            'employee_ids': employee_ids,
            'date_range': f'{start_date} to {end_date}',
            'record_ids': added_records[:50],  # Return first 50 IDs
            'errors': errors[:10],  # Return first 10 errors
            'source_records': len(attendance_data),
            'total_errors': len(errors)
        })
        
    except Exception as e:
        logger.error(f"Error in selective copy to staging: {str(e)}")
        
        # Log the failed operation
        try:
            log_staging_operation(
                operation_type='SELECTIVE_COPY',
                table_name='staging_attendance',
                operation_details='Failed to perform selective copy to staging',
                result_status='failure',
                error_details=str(e),
                user_ip=request.environ.get('REMOTE_ADDR', 'unknown'),
                user_agent=request.environ.get('HTTP_USER_AGENT', 'unknown')
            )
        except:
            pass
        
        return jsonify({
            'success': False,
            'error': f'Failed to perform selective copy: {str(e)}'
        }), 500

def parse_charge_job_data(charge_job_string):
    """
    Parse charge job data string according to the specified format with enhanced edge case handling.
    
    Supports multiple formats:
    1. 4-component: TaskCode / StationCode / MachineCode / ExpenseCode
    2. 3-component: TaskCode / StationCode / ExpenseCode (no Machine Code)
    3. 2-component: TaskCode / ExpenseCode (no Station Code, no Machine Code)
    
    Handles edge cases:
    - Task codes containing forward slash characters (e.g., "C/Roll Wages")
    - Mixed separator formats
    - Partial or malformed data
    
    Args:
        charge_job_string (str): The raw charge job string from the spreadsheet
        
    Returns:
        dict: Parsed components with keys: task_code, station_code, machine_code, expense_code
    """
    if not charge_job_string or not isinstance(charge_job_string, str):
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'invalid',
            'parse_error': 'Empty or invalid charge job string'
        }
    
    try:
        # Get separators from config
        primary_separator = config.get("charge_job_parsing", {}).get("primary_separator", " / ")
        fallback_separator = config.get("charge_job_parsing", {}).get("fallback_separator", "/")
        
        # Clean the string
        cleaned_string = charge_job_string.strip()
        
        # First attempt: Use primary separator (space-slash-space)
        parts = [part.strip() for part in cleaned_string.split(primary_separator)]
        parts = [part for part in parts if part]  # Remove empty parts
        
        # If primary separator doesn't work well, try fallback strategy
        if len(parts) <= 1 and fallback_separator in cleaned_string:
            # Use fallback parsing for edge cases like task codes with embedded slashes
            parts = parse_with_fallback_strategy(cleaned_string, primary_separator, fallback_separator)
        
        # Determine format based on number of components
        if len(parts) == 4:
            # 4-component format: TaskCode / StationCode / MachineCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': parts[2],
                'expense_code': parts[3],
                'format_type': '4_component',
                'parse_error': None
            }
        elif len(parts) == 3:
            # 3-component format: TaskCode / StationCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': '',  # No machine code in 3-component format
                'expense_code': parts[2],
                'format_type': '3_component',
                'parse_error': None
            }
        elif len(parts) == 2:
            # 2-component format: TaskCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': '',  # No station code in 2-component format
                'machine_code': '',  # No machine code in 2-component format
                'expense_code': parts[1],
                'format_type': '2_component',
                'parse_error': None
            }
        elif len(parts) == 1:
            # Single component - treat as task code only
            return {
                'task_code': parts[0],
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'format_type': '1_component',
                'parse_error': 'Only task code found, no separators'
            }
        else:
            # More than 4 components - try to handle gracefully
            return {
                'task_code': parts[0] if len(parts) > 0 else '',
                'station_code': parts[1] if len(parts) > 1 else '',
                'machine_code': parts[2] if len(parts) > 2 else '',
                'expense_code': parts[3] if len(parts) > 3 else '',
                'format_type': 'excessive_components',
                'parse_error': f'Found {len(parts)} components, expected 2-4. Using first 4 components.'
            }
            
    except Exception as e:
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'error',
            'parse_error': str(e)
        }

def parse_with_fallback_strategy(charge_job_string, primary_separator, fallback_separator):
    """
    Advanced parsing strategy to handle edge cases like task codes with embedded slashes.
    
    Strategy:
    1. Look for patterns that indicate legitimate separators vs embedded slashes
    2. Use context clues (spaces around separators, parentheses, etc.)
    3. Implement smart splitting based on common patterns
    
    Args:
        charge_job_string (str): The charge job string to parse
        primary_separator (str): Primary separator (space-slash-space)
        fallback_separator (str): Fallback separator (slash only)
        
    Returns:
        list: List of parsed components
    """
    try:
        # Strategy 1: Try to identify legitimate separators by looking for space patterns
        # Look for patterns like " / " which are more likely to be real separators
        if primary_separator in charge_job_string:
            # If we have primary separators, prefer those
            parts = [part.strip() for part in charge_job_string.split(primary_separator)]
            return [part for part in parts if part]
        
        # Strategy 2: If no primary separators, try intelligent fallback parsing
        # Split by fallback separator but try to identify task codes with embedded slashes
        tentative_parts = charge_job_string.split(fallback_separator)
        
        # Strategy 3: Enhanced heuristics for different scenarios
        if len(tentative_parts) >= 3:
            # Check for patterns that suggest we should combine parts for task codes
            
            # Scenario A: Simple format like "TaskCode/Station/Machine/Expense"
            # If parts look like structured data (no complex descriptions), treat as separate
            all_parts_simple = all(
                len(part.strip()) <= 30 and 
                '(' not in part and ')' not in part and
                ' AND ' not in part.upper() and
                ' OPERATION' not in part.upper()
                for part in tentative_parts
            )
            
            if all_parts_simple and len(tentative_parts) in [3, 4]:
                # This looks like a structured format: TaskCode/Station/Machine/Expense
                return [part.strip() for part in tentative_parts if part.strip()]
            
            # Scenario B: Task code with embedded slash (complex descriptions)
            first_part = tentative_parts[0].strip()
            second_part = tentative_parts[1].strip()
            
            # Enhanced heuristics for task codes with embedded slashes:
            # 1. Short first part + short second part (likely embedded slash)
            # 2. No complex descriptions in early parts
            # 3. Pattern like "Letter/Word" with reasonable length
            
            if (len(first_part) <= 15 and len(second_part) <= 25 and 
                '(' not in first_part and ')' not in first_part and
                '(' not in second_part and ')' not in second_part and
                ' STATION' not in second_part.upper() and
                ' STN-' not in second_part.upper() and
                'MCH-' not in second_part.upper()):
                
                # Likely a task code with embedded slash - combine first two parts
                combined_task_code = f"{first_part}/{second_part}"
                remaining_parts = [part.strip() for part in tentative_parts[2:] if part.strip()]
                
                result = [combined_task_code] + remaining_parts
                return result
        
        # Strategy 4: Default fallback - use tentative parts as-is
        return [part.strip() for part in tentative_parts if part.strip()]
        
    except Exception as e:
        # If all strategies fail, return original string as single component
        return [charge_job_string.strip()]

class EnhancedAttendanceReporter:
    """
    Enhanced wrapper for AttendanceReporter with database connection management.
    Provides robust error handling and automatic fallback functionality.
    """
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.reporter = None
        self.last_connection_attempt = None
        self.connection_errors = []
        self.max_retry_attempts = 3
        self.current_connection = None
        
        self._initialize_reporter()
    
    def _initialize_reporter(self):
        """Initialize the AttendanceReporter with database connection management."""
        try:
            # For fast startup, we'll defer connection testing until first use
            # This allows the application to start quickly without waiting for database validation
            
            # Create reporter instance immediately (connection will be tested on first use)
            self.reporter = AttendanceReporter()
            
            # Override the reporter's connection method to use our managed connection
            self.reporter.get_connection = self._get_managed_connection
            
            logger.info(f"✅ Enhanced AttendanceReporter initialized (connection deferred)")
            logger.info(f"🔗 Current connection mode: {self.db_manager.connection_mode}")
            logger.info(f"⚡ Database connections will be established on first use")
            
            return True
                
        except Exception as e:
            logger.warning(f"⚠️ AttendanceReporter initialization warning: {str(e)}")
            logger.info(f"📝 Reporter will attempt connection on first database operation")
            
            # Still return True to allow application startup
            # Connection issues will be handled when first database operation is attempted
            self.connection_errors.append({
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'connection_mode': self.db_manager.connection_mode,
                'context': 'initialization_deferred'
            })
            return True
    
    def _get_managed_connection(self):
        """Provide managed database connection with automatic retry and fallback."""
        try:
            # Check if current connection is still valid
            if self.current_connection:
                try:
                    # Test the connection with a simple query
                    cursor = self.current_connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.close()
                    return self.current_connection
                except Exception:
                    # Connection is invalid, get a new one
                    logger.warning("Current database connection invalid, getting new connection...")
                    self.current_connection = None
            
            # Get new connection through manager
            connection = self.db_manager.get_connection()
            if connection:
                self.current_connection = connection
                return connection
            else:
                raise Exception("No database connection available")
                
        except Exception as e:
            logger.error(f"Error getting managed connection: {str(e)}")
            raise
    
    def _execute_with_fallback(self, method_name, *args, **kwargs):
        """Execute a reporter method with automatic fallback and retry logic."""
        if not self.reporter:
            # Try to reinitialize the reporter
            if not self._initialize_reporter():
                raise Exception("AttendanceReporter not available and cannot be initialized")
        
        retry_count = 0
        last_error = None
        
        while retry_count < self.max_retry_attempts:
            try:
                # Get the method from the reporter
                method = getattr(self.reporter, method_name)
                
                # Execute the method
                result = method(*args, **kwargs)
                
                # Reset error tracking on success
                if retry_count > 0:
                    logger.info(f"✅ {method_name} succeeded after {retry_count} retries")
                
                return result
                
            except Exception as e:
                last_error = e
                retry_count += 1
                
                logger.warning(f"⚠️ {method_name} failed (attempt {retry_count}/{self.max_retry_attempts}): {str(e)}")
                
                if retry_count < self.max_retry_attempts:
                    # Try to reinitialize on connection errors
                    if any(keyword in str(e).lower() for keyword in ['connection', 'timeout', 'network']):
                        logger.info(f"Attempting to reinitialize connection for retry {retry_count}")
                        self._initialize_reporter()
                    
                    # Brief delay before retry
                    time.sleep(1)
                else:
                    # Final attempt failed
                    logger.error(f"❌ {method_name} failed after {self.max_retry_attempts} attempts: {str(e)}")
                    self.connection_errors.append({
                        'timestamp': datetime.now().isoformat(),
                        'method': method_name,
                        'error': str(e),
                        'retry_count': retry_count,
                        'connection_mode': self.db_manager.connection_mode
                    })
        
        # If all retries failed, raise the last error
        raise last_error
    
    def get_available_months(self, bus_code=None):
        """Get available months with fallback handling."""
        return self._execute_with_fallback('get_available_months', bus_code)
    
    def get_attendance_data(self, start_date, end_date, bus_code=None):
        """Get attendance data with fallback handling."""
        return self._execute_with_fallback('get_attendance_data', start_date, end_date, bus_code)
    
    def get_monthly_attendance_grid(self, year, month, bus_code=None):
        """Get monthly attendance grid with fallback handling."""
        return self._execute_with_fallback('get_monthly_attendance_grid', year, month, bus_code)
    
    def get_employees_list(self, bus_code=None):
        """Get employees list with fallback handling."""
        return self._execute_with_fallback('get_employees_list', bus_code)
    
    def get_shifts_list(self, bus_code=None):
        """Get shifts list with fallback handling."""
        return self._execute_with_fallback('get_shifts_list', bus_code)
    
    def get_monthly_summary(self, year, month, bus_code=None):
        """Get monthly summary with fallback handling."""
        return self._execute_with_fallback('get_monthly_summary', year, month, bus_code)
    
    def generate_daily_report(self, date, bus_code=None):
        """Generate daily report with fallback handling."""
        return self._execute_with_fallback('generate_daily_report', date, bus_code)
    
    def generate_date_range_report(self, start_date, end_date, bus_code=None):
        """Generate date range report with fallback handling."""
        return self._execute_with_fallback('generate_date_range_report', start_date, end_date, bus_code)
    
    def get_connection_health(self):
        """Get connection health information."""
        return {
            'reporter_available': self.reporter is not None,
            'current_connection_mode': self.db_manager.connection_mode,
            'connection_status': self.db_manager.get_connection_status(),
            'recent_errors': self.connection_errors[-5:] if self.connection_errors else [],
            'last_connection_attempt': self.last_connection_attempt
        }
    
    def force_reconnect(self):
        """Force reconnection to database."""
        logger.info("🔄 Forcing database reconnection...")
        self.current_connection = None
        self.reporter = None
        return self._initialize_reporter()
    
    def get_latest_available_data_date(self):
        """Get the latest available data date from HR_T_TAMachine_Summary table."""
        return self._execute_with_fallback('get_latest_available_data_date')

    def is_date_data_available(self, check_date, latest_available_date=None):
        """Check if data is available for a specific date."""
        return self._execute_with_fallback('is_date_data_available', check_date, latest_available_date)

    def get_available_dates_in_month(self, year, month):
        """Get list of dates in the specified month that have data available."""
        return self._execute_with_fallback('get_available_dates_in_month', year, month)
    
    def _match_ptrj_employee_id(self, venus_employee_name, ptrj_mapping):
        """Match PTRJ Employee ID for given Venus employee name."""
        return self._execute_with_fallback('_match_ptrj_employee_id', venus_employee_name, ptrj_mapping)

def log_staging_operation(operation_type, table_name, operation_details, affected_record_ids=None, 
                         data_volume=0, result_status='success', error_details=None, 
                         query_parameters=None, user_ip='unknown', user_agent='unknown'):
    """Log staging operations for audit and debugging purposes."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO staging_operations_log (
                operation_type, table_name, operation_details, affected_record_ids,
                data_volume, result_status, error_details, query_parameters,
                ip_address, user_agent
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            operation_type, table_name, operation_details,
            json.dumps(affected_record_ids) if affected_record_ids else None,
            data_volume, result_status, error_details,
            json.dumps(query_parameters) if query_parameters else None,
            user_ip, user_agent
        ))
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        logger.error(f"Failed to log staging operation: {e}")

# Initialize the enhanced attendance reporter
try:
    reporter = EnhancedAttendanceReporter(db_manager)
    logger.info("✅ Enhanced AttendanceReporter initialized (connections will be tested on-demand)")
except Exception as e:
    logger.warning(f"⚠️ Enhanced AttendanceReporter initialization warning: {e}")
    logger.info("📝 Application will continue startup - database connections can be tested via web interface")
    # Set reporter to None but continue startup
    reporter = None

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/test')
def test_page():
    """Simple test page without DataTables."""
    return render_template('simple_test.html')

@app.route('/test-grid')
def test_grid_page():
    """Test page for grid functionality."""
    return render_template('test_grid.html')

@app.route('/test-charge-jobs')
def test_charge_jobs_page():
    """Test page for employee charge jobs integration."""
    return send_file('test_frontend_integration.html')

@app.route('/test-staging')
def test_staging_page():
    """Test page for staging transfer debugging."""
    return send_file('test_staging_transfer.html')

@app.route('/api/staging/test', methods=['POST'])
def test_staging_endpoint():
    """Simple test endpoint to verify staging data processing."""
    try:
        logger.info("=== STAGING TEST ENDPOINT ===")
        logger.info(f"Content-Type: {request.headers.get('Content-Type')}")
        logger.info(f"Raw data length: {len(request.data) if request.data else 0}")
        
        data = request.get_json()
        logger.info(f"Parsed data type: {type(data)}")
        logger.info(f"Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        
        return jsonify({
            'success': True,
            'message': 'Test endpoint working',
            'received_data': {
                'type': str(type(data)),
                'keys': list(data.keys()) if isinstance(data, dict) else 'Not a dict',
                'data_preview': str(data)[:200] if data else 'No data'
            }
        })
        
    except Exception as e:
        logger.error(f"Test endpoint error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/debug-attendance')
def debug_attendance():
    """Debug endpoint to check raw attendance data."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code', 'PTRJ')

        # Get raw attendance data
        start_date = f"{year}-{month:02d}-01"
        end_date = f"{year}-{month:02d}-31"

        reporter = EnhancedAttendanceReporter(db_manager)
        raw_data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Get first 10 records for debugging
        debug_data = raw_data[:10] if raw_data else []

        return jsonify({
            'success': True,
            'total_records': len(raw_data),
            'sample_data': debug_data,
            'query_params': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'start_date': start_date,
                'end_date': end_date
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-grid-with-sample')
def test_grid_with_sample():
    """Test grid with sample working hours data."""
    try:
        # Generate sample data
        import calendar
        year = 2025
        month = 3
        days_in_month = calendar.monthrange(year, month)[1]

        # Sample employees
        employees = [
            {'EmployeeID': 'PTRJ.241000001', 'EmployeeName': 'John Doe'},
            {'EmployeeID': 'PTRJ.241000002', 'EmployeeName': 'Jane Smith'},
            {'EmployeeID': 'PTRJ.241000003', 'EmployeeName': 'Bob Wilson'},
        ]

        grid_data = []
        overtime_summary = []

        for i, emp in enumerate(employees, 1):
            days_data = {}
            total_regular = 0
            total_overtime = 0

            for day in range(1, days_in_month + 1):
                from datetime import datetime
                date_obj = datetime(year, month, day)
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday

                if weekday == 6:  # Sunday
                    days_data[str(day)] = 'OFF'
                elif day % 7 == 0:  # Some absent days
                    days_data[str(day)] = '-'
                elif weekday == 5:  # Saturday
                    hours = 4.5  # Saturday work
                    days_data[str(day)] = str(hours)
                    total_regular += hours
                else:  # Weekdays
                    if day % 5 == 0:  # Some overtime days
                        hours = 8.5
                        days_data[str(day)] = str(hours)
                        total_regular += 7
                        total_overtime += 1.5
                    else:
                        hours = 7.5
                        days_data[str(day)] = str(hours)
                        total_regular += hours

            grid_data.append({
                'No': i,
                'EmployeeID': emp['EmployeeID'],
                'EmployeeName': emp['EmployeeName'],
                'days': days_data
            })

            if total_overtime > 0:
                overtime_summary.append({
                    'No': i,
                    'EmployeeID': emp['EmployeeID'],
                    'EmployeeName': emp['EmployeeName'],
                    'TotalRegular': round(total_regular, 1),
                    'TotalOvertime': round(total_overtime, 1),
                    'TotalHours': round(total_regular + total_overtime, 1)
                })

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'month_name': 'March',
            'days_in_month': days_in_month,
            'total_employees': len(employees),
            'total_working_days': 22,
            'grid_data': grid_data,
            'overtime_summary': overtime_summary,
            'date_range': 'March 2025'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/monthly-grid-by-station')
def monthly_grid_by_station():
    """Get monthly attendance grid grouped by stations."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code')

        reporter = EnhancedAttendanceReporter(db_manager)
        grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)

        return jsonify({
            'success': True,
            'data': grid_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/test-monthly/<int:year>/<int:month>')
def test_monthly_direct(year, month):
    """Direct test for monthly report."""
    try:
        if reporter is None:
            return f"Error: Enhanced AttendanceReporter not initialized"

        # Test get_monthly_summary
        summary = reporter.get_monthly_summary(year, month, "PTRJ")

        # Test get_attendance_data
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        data = reporter.get_attendance_data(start_date, end_date, "PTRJ")

        return f"""
        <h1>Monthly Report Test: {year}-{month}</h1>
        <h2>Summary:</h2>
        <pre>{summary}</pre>
        <h2>Data Count:</h2>
        <p>Records found: {len(data)}</p>
        <h2>Sample Data:</h2>
        <pre>{data[:2] if data else 'No data'}</pre>
        """

    except Exception as e:
        import traceback
        return f"""
        <h1>Error Testing Monthly Report</h1>
        <p>Error: {str(e)}</p>
        <pre>{traceback.format_exc()}</pre>
        """

@app.route('/api/attendance')
def get_attendance_data():
    """API endpoint to get attendance data with filters."""
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required',
                'data': []
            }), 400

        # Get attendance data using enhanced reporter
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Apply additional filters
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]

        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        # Format data for JSON response
        formatted_data = []
        for record in data:
            formatted_record = {
                'EmployeeID': record.get('EmployeeID'),
                'EmployeeName': record.get('EmployeeName'),
                'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                'Shift': record.get('Shift'),
                'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                'DayOfWeek': record.get('DayOfWeek')
            }
            formatted_data.append(formatted_record)

        return jsonify({
            'success': True,
            'data': formatted_data,
            'total_records': len(formatted_data)
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/employees')
def get_employees():
    """API endpoint to get list of employees."""
    try:
        bus_code = request.args.get('bus_code')
        employees = reporter.get_employees_list(bus_code)

        return jsonify({
            'success': True,
            'data': employees
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/shifts')
def get_shifts():
    """API endpoint to get list of shifts."""
    try:
        bus_code = request.args.get('bus_code')
        shifts = reporter.get_shifts_list(bus_code)

        return jsonify({
            'success': True,
            'data': shifts
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/export')
def export_report():
    """API endpoint to export attendance report to Excel or JSON."""
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')
        format_type = request.args.get('format', 'excel')  # 'excel' or 'json'

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter data if specific employee or shift requested
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]
        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        if format_type == 'json':
            # JSON export
            import tempfile
            import json
            
            # Prepare JSON data with metadata
            json_data = {
                'metadata': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'bus_code': bus_code,
                    'employee_id': employee_id,
                    'shift': shift,
                    'export_date': datetime.now().isoformat(),
                    'total_records': len(data)
                },
                'attendance_data': []
            }
            
            # Format attendance data
            for record in data:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID', ''),
                    'EmployeeName': record.get('EmployeeName', ''),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else '',
                    'DayOfWeek': record.get('DayOfWeek', ''),
                    'Shift': record.get('Shift', ''),
                    'CheckIn': str(record.get('TACheckIn', '')) if record.get('TACheckIn') else '',
                    'CheckOut': str(record.get('TACheckOut', '')) if record.get('TACheckOut') else '',
                    'RegularHours': float(record.get('RegularHours', 0)),
                    'OvertimeHours': float(record.get('OvertimeHours', 0)),
                    'TotalHours': float(record.get('RegularHours', 0)) + float(record.get('OvertimeHours', 0))
                }
                json_data['attendance_data'].append(formatted_record)
            
            # Create temporary JSON file
            temp_dir = tempfile.gettempdir()
            filename = f"attendance_report_{start_date}_to_{end_date}.json"
            json_path = os.path.join(temp_dir, filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
            
            return send_file(json_path, as_attachment=True, download_name=filename)
            
        else:
            # Excel export (existing functionality)
            if start_date == end_date:
                # Daily report
                excel_path = reporter.generate_daily_report(start_date, bus_code)
            else:
                # Date range report
                excel_path = reporter.generate_date_range_report(start_date, end_date, bus_code)

            if excel_path and os.path.exists(excel_path):
                return send_file(
                    excel_path,
                    as_attachment=True,
                    download_name=os.path.basename(excel_path),
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                return jsonify({
                    'error': 'No data found or report generation failed'
                }), 404

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/summary')
def get_summary():
    """API endpoint to get attendance summary statistics."""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')

        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data using enhanced reporter
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Calculate summary statistics
        total_employees = len(set(record.get('EmployeeID') for record in data))
        total_records = len(data)
        total_regular_hours = sum(float(record.get('RegularHours', 0)) for record in data)
        total_overtime_hours = sum(float(record.get('OvertimeHours', 0)) for record in data)

        # Calculate average hours per employee
        avg_regular_hours = total_regular_hours / total_employees if total_employees > 0 else 0
        avg_overtime_hours = total_overtime_hours / total_employees if total_employees > 0 else 0

        summary = {
            'total_employees': total_employees,
            'total_records': total_records,
            'total_regular_hours': round(total_regular_hours, 2),
            'total_overtime_hours': round(total_overtime_hours, 2),
            'avg_regular_hours': round(avg_regular_hours, 2),
            'avg_overtime_hours': round(avg_overtime_hours, 2),
            'date_range': f"{start_date} to {end_date}"
        }

        return jsonify({
            'success': True,
            'data': summary
        })

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/months')
def get_available_months():
    """API endpoint to get list of available months with data."""
    try:
        # Enhanced debugging
        logger.info("=== GET AVAILABLE MONTHS DEBUG START ===")
        logger.info(f"Reporter status: {reporter is not None}")
        
        if reporter is None:
            logger.error("Enhanced AttendanceReporter not initialized")
            logger.error(f"Database manager status: {db_manager is not None}")
            if db_manager:
                logger.error(f"DB Manager connection mode: {db_manager.connection_mode}")
                logger.error(f"DB Manager status: {db_manager.get_connection_status()}")
            return jsonify({
                'error': 'Database connection not available',
                'data': [],
                'debug_info': {
                    'reporter_initialized': False,
                    'db_manager_available': db_manager is not None,
                    'connection_mode': db_manager.connection_mode if db_manager else 'unknown'
                }
            }), 500

        bus_code = request.args.get('bus_code')
        logger.info(f"Getting available months with bus_code: {bus_code}")

        # Test database connection first
        try:
            connection_health = reporter.get_connection_health()
            logger.info(f"Connection health: {connection_health}")
        except Exception as health_error:
            logger.error(f"Failed to get connection health: {health_error}")

        months = reporter.get_available_months(bus_code)
        logger.info(f"Retrieved {len(months)} months from reporter")
        logger.info(f"Raw months data: {months}")

        # Format months for frontend
        formatted_months = []
        for month in months:
            try:
                formatted_month = {
                    'year': month.get('Year'),
                    'month': month.get('Month'),
                    'month_name': month.get('MonthName'),
                    'record_count': month.get('RecordCount'),
                    'employee_count': month.get('EmployeeCount'),
                    'first_date': month.get('FirstDate').strftime('%Y-%m-%d') if month.get('FirstDate') else None,
                    'last_date': month.get('LastDate').strftime('%Y-%m-%d') if month.get('LastDate') else None,
                    'display_name': f"{month.get('MonthName')} {month.get('Year')}",
                    'month_key': f"{month.get('Year')}-{month.get('Month'):02d}"
                }
                formatted_months.append(formatted_month)
                logger.info(f"Formatted month: {formatted_month}")
            except Exception as format_error:
                logger.error(f"Error formatting month {month}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_months)} months")
        logger.info("=== GET AVAILABLE MONTHS DEBUG END ===")

        return jsonify({
            'success': True,
            'data': formatted_months,
            'debug_info': {
                'raw_months_count': len(months),
                'formatted_months_count': len(formatted_months),
                'bus_code': bus_code,
                'connection_mode': db_manager.connection_mode,
                'connection_health': reporter.get_connection_health() if reporter else None
            }
        })

    except Exception as e:
        logger.error(f"Error in get_available_months: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'data': [],
            'debug_info': {
                'error_type': type(e).__name__,
                'bus_code': request.args.get('bus_code'),
                'connection_mode': db_manager.connection_mode if db_manager else 'unknown',
                'reporter_available': reporter is not None
            }
        }), 500

@app.route('/api/monthly-report')
def get_monthly_report():
    """API endpoint to get monthly attendance report."""
    try:
        if reporter is None:
            logger.error("Enhanced AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly report for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get detailed attendance data for the month
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        # Calculate the last day of the month
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        logger.info(f"Getting attendance data from {start_date} to {end_date}")

        # Get attendance data using reporter
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        logger.info(f"Retrieved {len(data)} attendance records")

        # Format data for JSON response
        formatted_data = []
        for record in data:
            try:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID'),
                    'EmployeeName': record.get('EmployeeName'),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                    'Shift': record.get('Shift'),
                    'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                    'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                    'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                    'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                    'DayOfWeek': record.get('DayOfWeek')
                }
                formatted_data.append(formatted_record)
            except Exception as format_error:
                logger.error(f"Error formatting record {record}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_data)} records")

        return jsonify({
            'success': True,
            'data': formatted_data,
            'total_records': len(formatted_data),
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'date_range': f"{start_date} to {end_date}",
                'raw_records': len(data),
                'formatted_records': len(formatted_data),
                'connection_mode': db_manager.connection_mode
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_report: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__,
                'connection_mode': db_manager.connection_mode if db_manager else 'unknown'
            }
        }), 500

@app.route('/api/leave-data')
def get_leave_data():
    """API endpoint to get employee leave data."""
    try:
        if reporter is None:
            logger.error("Enhanced AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')

        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get leave data using reporter
        leave_data = reporter.get_leave_data(start_date, end_date, bus_code)

        # Apply employee filter if specified
        if employee_id:
            leave_data = [record for record in leave_data if record.get('EmployeeID') == employee_id]

        return jsonify({
            'success': True,
            'data': leave_data,
            'total_records': len(leave_data),
            'date_range': f"{start_date} to {end_date}"
        })

    except Exception as e:
        logger.error(f"Error getting leave data: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/monthly-grid')
def get_monthly_attendance_grid():
    """API endpoint to get monthly attendance in grid format (employee vs days) with leave integration."""
    try:
        if reporter is None:
            logger.error("Enhanced AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly grid for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get grid data using reporter (now includes leave data)
        logger.info("Getting monthly attendance grid with leave integration...")
        grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)
        logger.info(f"Grid data retrieved: {len(grid_data.get('grid_data', []))} employees")

        return jsonify({
            'success': True,
            'data': grid_data,
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'total_employees': grid_data.get('total_employees', 0),
                'days_in_month': grid_data.get('days_in_month', 0),
                'connection_mode': db_manager.connection_mode,
                'leave_integration': True
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_attendance_grid: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__,
                'connection_mode': db_manager.connection_mode if db_manager else 'unknown'
            }
        }), 500

@app.route('/api/staging/move-to-staging', methods=['POST'])
def move_to_staging():
    """Move attendance records from main data to staging."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Get filters for selecting records to move
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        employee_ids = data.get('employee_ids', [])
        bus_code = data.get('bus_code')
        
        if not start_date or not end_date:
            return jsonify({
                'success': False,
                'error': 'start_date and end_date are required'
            }), 400
        
        # Get data availability information to filter out unavailable dates
        from datetime import datetime
        start_year = datetime.strptime(start_date, '%Y-%m-%d').year
        start_month = datetime.strptime(start_date, '%Y-%m-%d').month
        end_year = datetime.strptime(end_date, '%Y-%m-%d').year
        end_month = datetime.strptime(end_date, '%Y-%m-%d').month
        
        # Get latest available data date
        latest_available_date = reporter.get_latest_available_data_date()
        
        # Get attendance data from main system using enhanced reporter
        attendance_data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter out records with dates beyond the latest available data
        if latest_available_date:
            original_count = len(attendance_data)
            attendance_data = [
                record for record in attendance_data 
                if reporter.is_date_data_available(
                    record.get('TADate').date() if hasattr(record.get('TADate'), 'date') else record.get('TADate'),
                    latest_available_date
                )
            ]
            filtered_count = len(attendance_data)
            if original_count > filtered_count:
                logger.info(f"Filtered out {original_count - filtered_count} records with unavailable dates (beyond {latest_available_date})")
        
        # Get PTRJ Employee ID mapping
        ptrj_mapping = {}
        try:
            ptrj_mapping = reporter.get_ptrj_employee_mapping()
            logger.info(f"Retrieved PTRJ mapping for {len(ptrj_mapping)} entries")
        except Exception as e:
            logger.warning(f"Could not retrieve PTRJ mapping: {e}")
        
        # Filter by employee IDs if specified
        if employee_ids:
            attendance_data = [record for record in attendance_data if record.get('EmployeeID') in employee_ids]
        
        if not attendance_data:
            return jsonify({
                'success': False,
                'error': 'No attendance records found for the specified criteria'
            }), 404
        
        # Convert attendance data to staging format
        staging_records = []
        for record in attendance_data:
            employee_name = record.get('EmployeeName', '')
            
            # Get PTRJ Employee ID for this employee
            ptrj_employee_id = reporter._match_ptrj_employee_id(employee_name, ptrj_mapping) if hasattr(reporter, '_match_ptrj_employee_id') else "N/A"
            
            # Handle date formatting - TADate might be string or datetime
            ta_date = record.get('TADate')
            if ta_date:
                if hasattr(ta_date, 'strftime'):
                    date_str = ta_date.strftime('%Y-%m-%d')
                    source_id_date = ta_date.strftime('%Y%m%d')
                else:
                    # TADate is already a string
                    date_str = str(ta_date)[:10]  # Get YYYY-MM-DD part
                    source_id_date = str(ta_date).replace('-', '')[:8]  # Get YYYYMMDD
            else:
                date_str = ''
                source_id_date = ''
            
            # Handle time formatting - might be string or datetime
            check_in = record.get('TACheckIn')
            check_in_str = ''
            if check_in:
                if hasattr(check_in, 'strftime'):
                    check_in_str = check_in.strftime('%H:%M')
                else:
                    check_in_str = str(check_in)[:5]  # Get HH:MM part
            
            check_out = record.get('TACheckOut')
            check_out_str = ''
            if check_out:
                if hasattr(check_out, 'strftime'):
                    check_out_str = check_out.strftime('%H:%M')
                else:
                    check_out_str = str(check_out)[:5]  # Get HH:MM part
            
            staging_record = {
                'employee_id': record.get('EmployeeID', ''),
                'employee_name': employee_name,
                'ptrj_employee_id': ptrj_employee_id,
                'date': date_str,
                'day_of_week': record.get('DayOfWeek', ''),
                'shift': record.get('Shift', ''),
                'check_in': check_in_str,
                'check_out': check_out_str,
                'regular_hours': float(record.get('RegularHours', 0)),
                'overtime_hours': float(record.get('OvertimeHours', 0)),
                'task_code': '',  # To be filled from charge job data
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'raw_charge_job': '',  # To be filled from charge job data
                'source_record_id': f"{record.get('EmployeeID', '')}_{source_id_date}",
                'notes': 'Moved from main attendance data'
            }
            staging_records.append(staging_record)
        
        # Add records to staging database
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        added_records = []
        errors = []
        
        for record in staging_records:
            try:
                staging_id = str(uuid.uuid4())
                
                # Extract leave data if available
                leave_data = record.get('leave_data')
                leave_type_code = leave_data.get('leave_type_code') if leave_data else None
                leave_type_description = leave_data.get('leave_type_description') if leave_data else None
                leave_ref_number = leave_data.get('ref_number') if leave_data else None
                is_alfa = record.get('status') == 'alfa'
                is_on_leave = record.get('status') == 'on_leave'

                cursor.execute('''
                    INSERT INTO staging_attendance (
                        id, employee_id, employee_name, ptrj_employee_id, date, day_of_week, shift,
                        check_in, check_out, regular_hours, overtime_hours, total_hours,
                        task_code, station_code, machine_code, expense_code, raw_charge_job,
                        leave_type_code, leave_type_description, leave_ref_number,
                        is_alfa, is_on_leave, status, source_record_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    staging_id, record['employee_id'], record['employee_name'], record['ptrj_employee_id'],
                    record['date'], record['day_of_week'], record['shift'],
                    record['check_in'], record['check_out'],
                    record['regular_hours'], record['overtime_hours'],
                    record['regular_hours'] + record['overtime_hours'],
                    record['task_code'], record['station_code'],
                    record['machine_code'], record['expense_code'], record['raw_charge_job'],
                    leave_type_code, leave_type_description, leave_ref_number,
                    is_alfa, is_on_leave, 'staged',  # Default status
                    record['source_record_id'], record['notes']
                ))
                
                added_records.append(staging_id)
                
            except Exception as record_error:
                errors.append({
                    'record': record,
                    'error': str(record_error)
                })
                continue
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'Successfully moved {len(added_records)} records to staging',
            'moved_records': len(added_records),
            'record_ids': added_records,
            'errors': errors,
            'source_records': len(attendance_data)
        })
        
    except Exception as e:
        logger.error(f"Error moving records to staging: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to move records to staging: {str(e)}'
        }), 500

# Database Connection Management API Endpoints

@app.route('/api/database/status', methods=['GET'])
def get_database_status():
    """Get current database connection status with enhanced error handling."""
    try:
        status = db_manager.get_connection_status()
        
        # Add additional status information
        status['application_health'] = {
            'enhanced_reporter': reporter is not None,
            'db_manager': db_manager is not None,
            'staging_db': check_staging_db_health()
        }
        
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"Error getting database status: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get database status: {str(e)}',
            'fallback_status': {
                'local_status': {'connected': False, 'error': 'Status check failed'},
                'remote_status': {'connected': False, 'error': 'Status check failed'},
                'current_mode': 'unknown'
            }
        }), 500

@app.route('/api/database/test-connection', methods=['POST'])
def test_database_connection():
    """Test database connection for specified mode or all connections."""
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json() or {}
        else:
            # If not JSON, try to get data from form
            data = {}
            if request.form:
                data = request.form.to_dict()
            elif request.data:
                try:
                    import json
                    data = json.loads(request.data.decode('utf-8'))
                except:
                    data = {}
        
        mode = data.get('mode', 'all')  # Default to testing all connections
        custom_config = data.get('config')  # Optional custom configuration for testing
        
        # Validate mode parameter
        if mode not in ['local', 'remote', 'all']:
            return jsonify({
                'success': False,
                'error': 'Invalid mode. Must be "local", "remote", or "all".'
            }), 400
        
        results = {}
        
        if mode == 'all':
            # Test both connections
            logger.info("Testing all database connections...")
            
            # Test local connection
            local_success, local_message = db_manager.test_connection('local')
            results['local'] = {
                'success': local_success,
                'message': local_message,
                'mode': 'local',
                'timestamp': datetime.now().isoformat()
            }
            
            # Test remote connection
            remote_success, remote_message = db_manager.test_connection('remote')
            results['remote'] = {
                'success': remote_success,
                'message': remote_message,
                'mode': 'remote',
                'timestamp': datetime.now().isoformat()
            }
            
            overall_success = local_success or remote_success
            
        else:
            # Test specific connection
            logger.info(f"Testing {mode} database connection...")
            success, message = db_manager.test_connection(mode, config=custom_config)
            
            results[mode] = {
                'success': success,
                'message': message,
                'mode': mode,
                'timestamp': datetime.now().isoformat()
            }
            
            overall_success = success
        
        # Get current status after testing
        current_status = db_manager.get_status()
        
        response_data = {
            'success': True,  # API call succeeded
            'overall_success': overall_success,
            'results': results,
            'status': current_status,
            'timestamp': datetime.now().isoformat()
        }
        
        # Add suggestions based on results
        if mode == 'all':
            if results['local']['success'] and not results['remote']['success']:
                response_data['suggestion'] = 'Local database is available. Consider using local mode.'
            elif not results['local']['success'] and results['remote']['success']:
                response_data['suggestion'] = 'Remote database is available. Consider using remote mode.'
            elif not results['local']['success'] and not results['remote']['success']:
                response_data['suggestion'] = 'No database connections available. Check network and database server status.'
            else:
                response_data['suggestion'] = 'Both connections available. Current mode is optimal.'
        
        return jsonify(response_data), 200
            
    except Exception as e:
        error_msg = f"Database connection test error: {str(e)}"
        logger.error(error_msg)
        return jsonify({
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/database/initial-scan', methods=['POST'])
def initial_database_scan():
    """Perform initial database connection scan with quick timeouts."""
    try:
        logger.info("🔍 Starting initial database connection scan...")
        
        # Test both connections with quick timeouts for initial scan
        results = {}
        
        # Test local connection
        local_success, local_message = db_manager.test_connection('local')
        results['local'] = {
            'connected': local_success,
            'message': local_message,
            'last_attempt': datetime.now().isoformat(),
            'error': None if local_success else local_message
        }
        
        # Test remote connection with quick timeout
        remote_success, remote_message = db_manager.test_connection('remote')
        results['remote'] = {
            'connected': remote_success,
            'message': remote_message,
            'last_attempt': datetime.now().isoformat(),
            'error': None if remote_success else remote_message
        }
        
        # Update internal status
        db_manager.connection_status.update(results)
        
        scan_summary = {
            'local_available': local_success,
            'remote_available': remote_success,
            'any_available': local_success or remote_success,
            'both_available': local_success and remote_success,
            'current_mode': db_manager.connection_mode,
            'scan_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Initial scan complete: Local={local_success}, Remote={remote_success}")
        
        return jsonify({
            'success': True,
            'results': results,
            'summary': scan_summary,
            'status': db_manager.get_status()
        })
        
    except Exception as e:
        error_msg = f"Initial database scan error: {str(e)}"
        logger.error(error_msg)
        return jsonify({
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/database/switch-mode', methods=['POST'])
def switch_database_mode():
    """Switch database connection mode with enhanced error handling."""
    try:
        # Validate request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No JSON data provided'
            }), 400

        new_mode = data.get('mode')
        if new_mode not in ['local', 'remote']:
            return jsonify({
                'success': False,
                'error': 'Invalid connection mode. Must be "local" or "remote".'
            }), 400

        logger.info(f"🔄 Attempting to switch database mode to: {new_mode}")

        # Test the new connection before switching with timeout protection
        try:
            test_success, test_message = db_manager.test_connection(new_mode)
            logger.info(f"Connection test result for {new_mode}: {test_success} - {test_message}")
        except Exception as test_error:
            logger.error(f"Connection test failed with exception: {str(test_error)}")
            return jsonify({
                'success': False,
                'message': f'Connection test failed: {str(test_error)}',
                'current_mode': db_manager.connection_mode,
                'status': db_manager.get_connection_status()
            }), 400

        if not test_success:
            return jsonify({
                'success': False,
                'message': f'Cannot switch to {new_mode} database: {test_message}',
                'current_mode': db_manager.connection_mode,
                'status': db_manager.get_connection_status(),
                'fallback_suggestion': suggest_fallback_mode(new_mode)
            }), 400

        # Proceed with switching
        success, message = db_manager.switch_connection_mode(new_mode)

        if success:
            # Update config file
            try:
                config['database_config']['connection_mode'] = new_mode
                db_manager.update_database_config({'connection_mode': new_mode})
                logger.info(f"✅ Successfully switched to {new_mode} database mode")
            except Exception as config_error:
                logger.warning(f"Failed to update config file: {str(config_error)}")
                # Don't fail the request if config update fails

        return jsonify({
            'success': success,
            'message': message,
            'current_mode': db_manager.connection_mode,
            'status': db_manager.get_connection_status(),
            'switch_timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"❌ Error switching database mode: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to switch database mode: {str(e)}',
            'current_mode': db_manager.connection_mode if db_manager else 'unknown',
            'error_timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/database/update-config', methods=['POST'])
def update_database_config():
    """Update database configuration with comprehensive validation."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No configuration data provided'
            }), 400
        
        # Validate configuration data
        validation_result = validate_database_config(data)
        if not validation_result['valid']:
            return jsonify({
                'success': False,
                'error': f'Invalid configuration: {validation_result["error"]}',
                'validation_details': validation_result['details']
            }), 400
        
        # Test connections before saving
        mode = data.get('mode', 'local')
        db_config = data.get('database_config', {})
        
        if mode in ['local', 'remote']:
            # Temporarily update config for testing
            original_config = db_manager.db_config.copy()
            
            try:
                if mode == 'local' and 'local_database' in db_config:
                    db_manager.db_config['local_database'].update(db_config['local_database'])
                elif mode == 'remote' and 'remote_database' in db_config:
                    db_manager.db_config['remote_database'].update(db_config['remote_database'])
                
                # Test the connection
                success, error_message = db_manager.test_connection(mode)
                
                if not success:
                    # Restore original config
                    db_manager.db_config = original_config
                    return jsonify({
                        'success': False,
                        'error': f'Connection test failed: {error_message}',
                        'test_mode': mode,
                        'fallback_available': check_fallback_availability()
                    }), 400
                    
            except Exception as test_error:
                # Restore original config
                db_manager.db_config = original_config
                return jsonify({
                    'success': False,
                    'error': f'Connection test error: {str(test_error)}'
                }), 400
        
        # Save the configuration
        success, message = db_manager.update_database_config(db_config)
        
        if success and 'connection_mode' in data:
            db_manager.switch_connection_mode(data['connection_mode'])
        
        return jsonify({
            'success': success,
            'message': message,
            'status': db_manager.get_connection_status(),
            'updated_timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error updating database configuration: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to update database configuration: {str(e)}'
        }), 500

@app.route('/api/database/config', methods=['GET'])
def get_database_config():
    """Get current database configuration without sensitive information."""
    try:
        # Return configuration without sensitive information
        safe_config = {
            'connection_mode': db_manager.connection_mode,
            'fallback_enabled': db_manager.fallback_enabled,
            'local_database': {
                'server': db_manager.db_config.get('local_database', {}).get('server', 'localhost'),
                'database': db_manager.db_config.get('local_database', {}).get('database', 'VenusHR14'),
                'port': db_manager.db_config.get('local_database', {}).get('port', 1433),
                'username': db_manager.db_config.get('local_database', {}).get('username', 'sa'),
                'authentication': db_manager.db_config.get('local_database', {}).get('authentication', 'sql_server')
            },
            'remote_database': {
                'server': db_manager.db_config.get('remote_database', {}).get('server', '********'),
                'database': db_manager.db_config.get('remote_database', {}).get('database', 'VenusHR14'),
                'port': db_manager.db_config.get('remote_database', {}).get('port', 1888),
                'username': db_manager.db_config.get('remote_database', {}).get('username', 'sa'),
                'authentication': db_manager.db_config.get('remote_database', {}).get('authentication', 'sql_server')
            },
            'status': db_manager.get_connection_status(),
            'health_check': {
                'enhanced_reporter_available': reporter is not None,
                'staging_db_available': check_staging_db_health()
            }
        }
        
        return jsonify({
            'success': True,
            'config': safe_config
        })
        
    except Exception as e:
        logger.error(f"Error getting database configuration: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get database configuration: {str(e)}'
        }), 500

@app.route('/api/employee-charge-jobs')
def get_employee_charge_jobs():
    """
    Fetch employee charge job data from Google Apps Script API with robust error handling.
    Returns parsed task codes, station codes, machine codes, and expense codes.
    """
    try:
        logger.info(f"Fetching employee charge job data from: {CHARGE_JOB_DATA_URL}")
        
        # Check if we have database connectivity for fallback
        db_available = reporter is not None
        connection_mode = db_manager.connection_mode if db_manager else 'unknown'
        
        # Make GET request to Google Apps Script for charge job data
        timeout = config.get("sync_settings", {}).get("timeout_seconds", 30)
        
        try:
            response = requests.get(CHARGE_JOB_DATA_URL, timeout=timeout)
            response.raise_for_status()
        except requests.exceptions.Timeout:
            logger.error("Timeout while fetching charge job data from Google Apps Script")
            return handle_charge_jobs_fallback("Request timeout", timeout, db_available, connection_mode)
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error while fetching charge job data: {str(e)}")
            return handle_charge_jobs_fallback("Connection error", str(e), db_available, connection_mode)
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error while fetching charge job data: {str(e)}")
            return handle_charge_jobs_fallback("Request failed", str(e), db_available, connection_mode)
        
        # Parse the response
        try:
            employee_data = response.json()
            logger.info(f"Retrieved response from Google Apps Script")
            logger.info(f"Response type: {type(employee_data)}")
            
            # Handle different response formats
            if isinstance(employee_data, dict):
                # If response is wrapped in a result object
                if 'data' in employee_data:
                    employee_list = employee_data['data']
                elif 'employees' in employee_data:
                    employee_list = employee_data['employees']
                else:
                    # Assume the dict itself contains employee data
                    employee_list = [employee_data]
            else:
                # Assume it's already a list
                employee_list = employee_data
            
            logger.info(f"Processing {len(employee_list)} employee records")
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response text: {response.text[:500]}...")
            return handle_charge_jobs_fallback("Invalid JSON response", response.text[:200], db_available, connection_mode)
        
        # Process and parse the charge job data
        processed_data = {}
        parse_errors = []
        format_stats = {'4_component': 0, '3_component': 0, '2_component': 0, '1_component': 0, 
                       'partial': 0, 'error': 0, 'invalid': 0, 'excessive_components': 0}
        
        for i, emp in enumerate(employee_list):
            try:
                # Handle different field name variations for employee identification
                emp_name = (emp.get('namaKaryawan') or 
                           emp.get('employeeName') or 
                           emp.get('EmployeeName') or 
                           emp.get('name') or '').strip()
                
                emp_id = (emp.get('employeeId') or 
                         emp.get('EmployeeID') or 
                         emp.get('employeeID') or 
                         emp.get('id') or '').strip()
                
                # Handle different field name variations for charge job data
                charge_job = (emp.get('chargeJob') or 
                             emp.get('charge_job') or 
                             emp.get('ChargeJob') or 
                             emp.get('task_code_data') or '').strip()
                
                # Use employee ID as primary key, fallback to name
                employee_key = emp_id if emp_id else emp_name
                
                if employee_key and charge_job:
                    # Parse the charge job data using our parsing function
                    parsed_data = parse_charge_job_data(charge_job)
                    
                    # Track format statistics
                    format_type = parsed_data.get('format_type', 'unknown')
                    if format_type in format_stats:
                        format_stats[format_type] += 1
                    
                    # Log parsing errors for debugging
                    if parsed_data.get('parse_error'):
                        parse_errors.append({
                            'employee': employee_key,
                            'charge_job': charge_job,
                            'error': parsed_data['parse_error']
                        })
                    
                    # Prepare the final employee data
                    processed_data[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'task_code': parsed_data['task_code'],
                        'station_code': parsed_data['station_code'],
                        'machine_code': parsed_data['machine_code'], 
                        'expense_code': parsed_data['expense_code'],
                        'raw_charge_job': charge_job,
                        'format_type': parsed_data['format_type'],
                        'parse_error': parsed_data['parse_error'],
                        
                        # Additional employee data if available
                        'gender': emp.get('Gender') or emp.get('gender', ''),
                        'station': emp.get('Nama Stasiun') or emp.get('station', ''),
                        'no_urut': emp.get('No. Urut') or emp.get('sequence_number', '')
                    }
                else:
                    # Log missing data
                    missing_fields = []
                    if not employee_key:
                        missing_fields.append('employee identifier (name/ID)')
                    if not charge_job:
                        missing_fields.append('charge job data')
                    
                    logger.warning(f"Skipping employee record {i+1}: missing {', '.join(missing_fields)}")
                    
            except Exception as emp_error:
                logger.error(f"Error processing employee record {i+1}: {emp_error}")
                parse_errors.append({
                    'employee': f'Record {i+1}',
                    'charge_job': str(emp),
                    'error': str(emp_error)
                })
                continue
        
        logger.info(f"Successfully processed {len(processed_data)} employee charge job records")
        logger.info(f"Format statistics: {format_stats}")
        
        if parse_errors:
            logger.warning(f"Encountered {len(parse_errors)} parsing errors")
            for error in parse_errors[:5]:  # Log first 5 errors
                logger.warning(f"  - {error['employee']}: {error['error']}")
        
        return jsonify({
            'success': True,
            'data': processed_data,
            'total_records': len(processed_data),
            'source_url': CHARGE_JOB_DATA_URL,
            'format_statistics': format_stats,
            'parse_errors': len(parse_errors),
            'database_status': {
                'available': db_available,
                'connection_mode': connection_mode
            },
            'debug_info': {
                'raw_employee_count': len(employee_list),
                'processed_count': len(processed_data),
                'sample_errors': parse_errors[:3] if parse_errors else []
            }
        })
        
    except Exception as e:
        logger.error(f"Error processing employee charge job data: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Provide fallback response
        db_available = reporter is not None
        connection_mode = db_manager.connection_mode if db_manager else 'unknown'
        return handle_charge_jobs_fallback("Processing error", str(e), db_available, connection_mode)

def handle_charge_jobs_fallback(error_type, error_details, db_available, connection_mode):
    """Handle fallback scenarios for charge jobs endpoint."""
    fallback_response = {
        'success': False,
        'error': f'{error_type}: {error_details}',
        'fallback_mode': True,
        'database_status': {
            'available': db_available,
            'connection_mode': connection_mode
        },
        'source_url': CHARGE_JOB_DATA_URL,
        'suggestions': [
            'Check your internet connection',
            'Verify Google Apps Script URL is accessible',
            'Try again in a few moments'
        ]
    }
    
    # If database is available, suggest using basic employee data
    if db_available:
        fallback_response['database_fallback'] = {
            'available': True,
            'message': 'Employee list is available from database',
            'suggestion': 'You can still access basic employee information and attendance data'
        }
    
    return jsonify(fallback_response), 503

def check_staging_db_health():
    """Check if staging database is healthy."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM staging_attendance LIMIT 1')
        conn.close()
        return True
    except:
        return False

def classify_connection_error(error_message):
    """Classify database connection errors for better user feedback."""
    error_lower = error_message.lower()
    
    if 'timeout' in error_lower:
        return 'timeout'
    elif 'login' in error_lower or 'authentication' in error_lower:
        return 'authentication'
    elif 'network' in error_lower or 'tcp provider' in error_lower:
        return 'network'
    elif 'server is not found' in error_lower or 'not accessible' in error_lower:
        return 'server_unreachable'
    else:
        return 'unknown'

def get_troubleshooting_hints(mode, error_message):
    """Provide troubleshooting hints based on connection mode and error."""
    error_type = classify_connection_error(error_message)
    
    base_hints = {
        'timeout': [
            'Check if the database server is running',
            'Verify network connectivity',
            'Consider increasing connection timeout'
        ],
        'authentication': [
            'Check username and password',
            'Verify SQL Server authentication is enabled',
            'Ensure user has proper permissions'
        ],
        'network': [
            'Check firewall settings',
            'Verify port is open and accessible',
            'Test network connectivity to the server'
        ],
        'server_unreachable': [
            'Verify server IP address is correct',
            'Check if server is running',
            'Confirm port number is correct'
        ]
    }
    
    mode_specific = {
        'local': ['Check if SQL Server service is running locally'],
        'remote': ['Verify VPN connection if required', 'Check remote server accessibility']
    }
    
    hints = base_hints.get(error_type, ['Check connection settings'])
    hints.extend(mode_specific.get(mode, []))
    
    return hints

def suggest_fallback_mode(failed_mode):
    """Suggest fallback mode when primary mode fails."""
    fallback_mode = 'local' if failed_mode == 'remote' else 'remote'
    
    # Test if fallback is available
    if db_manager:
        success, _ = db_manager.test_connection(fallback_mode)
        if success:
            return {
                'available': True,
                'mode': fallback_mode,
                'message': f'Consider switching to {fallback_mode} database'
            }
    
    return {
        'available': False,
        'message': 'No fallback connection available'
    }

def check_fallback_availability():
    """Check if any database connection is available for fallback."""
    if not db_manager:
        return False
    
    local_available, _ = db_manager.test_connection('local')
    remote_available, _ = db_manager.test_connection('remote')
    
    return local_available or remote_available

def validate_database_config(config_data):
    """Validate database configuration data."""
    result = {'valid': True, 'error': None, 'details': []}
    
    try:
        mode = config_data.get('mode', 'local')
        db_config = config_data.get('database_config', {})
        
        if mode not in ['local', 'remote']:
            result['valid'] = False
            result['error'] = 'Invalid mode'
            result['details'].append('Mode must be "local" or "remote"')
        
        # Validate database configuration
        if mode == 'remote' and 'remote_database' in db_config:
            remote_config = db_config['remote_database']
            required_fields = ['server', 'port', 'username', 'password']
            missing_fields = [field for field in required_fields if not remote_config.get(field)]
            
            if missing_fields:
                result['valid'] = False
                result['error'] = f'Missing required remote database fields'
                result['details'].extend([f'Missing field: {field}' for field in missing_fields])
        
        # Validate port numbers
        for db_type in ['local_database', 'remote_database']:
            if db_type in db_config and 'port' in db_config[db_type]:
                port = db_config[db_type]['port']
                if not isinstance(port, int) or port < 1 or port > 65535:
                    result['valid'] = False
                    result['error'] = f'Invalid port number for {db_type}'
                    result['details'].append(f'{db_type} port must be between 1 and 65535')
        
    except Exception as e:
        result['valid'] = False
        result['error'] = f'Configuration validation error: {str(e)}'
    
    return result

@app.route('/fallback')
def database_fallback_page():
    """Fallback page when both databases are unavailable."""
    return render_template('database_fallback.html')

# Specific debug endpoint for months issue
@app.route('/api/debug-months')
def debug_months():
    """Debug endpoint specifically for months issue."""
    try:
        debug_info = {
            'timestamp': datetime.now().isoformat(),
            'reporter_status': {
                'initialized': reporter is not None,
                'health': reporter.get_connection_health() if reporter else None
            },
            'database_manager': {
                'initialized': db_manager is not None,
                'connection_mode': db_manager.connection_mode if db_manager else None,
                'status': db_manager.get_connection_status() if db_manager else None
            },
            'config': {
                'connection_mode': config.get('database_config', {}).get('connection_mode'),
                'local_server': config.get('database_config', {}).get('local_database', {}).get('server'),
                'local_database': config.get('database_config', {}).get('local_database', {}).get('database')
            }
        }
        
        # Test raw database query
        if reporter:
            try:
                # Try to get raw data from database
                test_query_result = reporter.reporter.db_connection.execute_query(
                    "SELECT TOP 5 * FROM HR_T_TAMachine_Summary WHERE BusCode = 'PTRJ' ORDER BY TADate DESC",
                    ()
                )
                debug_info['test_query'] = {
                    'success': True,
                    'record_count': len(test_query_result),
                    'sample_records': test_query_result[:2] if test_query_result else []
                }
            except Exception as query_error:
                debug_info['test_query'] = {
                    'success': False,
                    'error': str(query_error)
                }
        
        return jsonify(debug_info)
        
    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# Debug endpoint
@app.route('/api/debug')
def debug_info():
    """Debug endpoint to check system status."""
    try:
        debug_data = {
            'enhanced_reporter_status': 'initialized' if reporter else 'failed',
            'db_manager_status': 'initialized' if db_manager else 'failed',
            'connection_mode': db_manager.connection_mode if db_manager else 'unknown',
            'python_path': sys.path[:3],
            'current_directory': os.getcwd(),
            'templates_exist': os.path.exists('templates'),
            'static_exist': os.path.exists('static')
        }

        if reporter:
            try:
                # Test database connection
                test_months = reporter.get_available_months()
                debug_data['database_test'] = {
                    'status': 'success',
                    'months_found': len(test_months),
                    'sample_month': test_months[0] if test_months else None
                }
            except Exception as db_error:
                debug_data['database_test'] = {
                    'status': 'failed',
                    'error': str(db_error)
                }

        return jsonify({
            'success': True,
            'debug_data': debug_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/database/test-all-connections', methods=['POST'])
def test_all_database_connections():
    """Test both local and remote database connections simultaneously."""
    try:
        logger.info("Testing all database connections...")
        
        # Test both connections in parallel for efficiency
        local_result = db_manager.test_connection('local')
        remote_result = db_manager.test_connection('remote')
        
        # Prepare results
        results = {
            'local': {
                'success': local_result[0],
                'message': local_result[1],
                'mode': 'local',
                'timestamp': datetime.now().isoformat()
            },
            'remote': {
                'success': remote_result[0],
                'message': remote_result[1],
                'mode': 'remote',
                'timestamp': datetime.now().isoformat()
            }
        }
        
        # Add error classification and troubleshooting hints for failures
        for mode, result in results.items():
            if not result['success']:
                result['error_classification'] = classify_connection_error(result['message'])
                result['troubleshooting_hints'] = get_troubleshooting_hints(mode, result['message'])
        
        # Determine overall success
        overall_success = local_result[0] or remote_result[0]  # At least one should work
        
        response_data = {
            'success': True,  # API call succeeded
            'overall_connection_success': overall_success,
            'results': results,
            'status': db_manager.get_connection_status(),
            'summary': {
                'local_available': local_result[0],
                'remote_available': remote_result[0],
                'current_mode': db_manager.connection_mode,
                'fallback_available': local_result[0] and remote_result[0]
            }
        }
        
        # Add suggestions based on results
        if local_result[0] and not remote_result[0]:
            response_data['suggestion'] = 'Local database is available. Consider using local mode.'
        elif not local_result[0] and remote_result[0]:
            response_data['suggestion'] = 'Remote database is available. Consider using remote mode.'
        elif not local_result[0] and not remote_result[0]:
            response_data['suggestion'] = 'No database connections available. Check network and database server status.'
        else:
            response_data['suggestion'] = 'Both connections available. Current mode is optimal.'
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error testing all database connections: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to test database connections: {str(e)}',
            'fallback_available': check_fallback_availability(),
            'timestamp': datetime.now().isoformat()
        }), 500

# NOTE: This endpoint is deprecated - use /api/database/test-connection with mode='all' instead

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    # Get server configuration
    server_config = config.get("server_config", {})
    port = server_config.get("port", 5173)
    host = server_config.get("host", "0.0.0.0")
    debug = server_config.get("debug", True)

    # Print startup information
    print("=" * 60)
    print("ATTENDANCE REPORT WEB APPLICATION")
    print("=" * 60)
    print(f"Current directory: {os.getcwd()}")
    print(f"Enhanced Reporter status: {'OK' if reporter else 'FAILED'}")
    print(f"Database Manager status: {'OK' if db_manager else 'FAILED'}")
    print(f"Connection Mode: {db_manager.connection_mode if db_manager else 'Unknown'}")
    print(f"Templates directory: {'EXISTS' if os.path.exists('templates') else 'MISSING'}")
    print(f"Static directory: {'EXISTS' if os.path.exists('static') else 'MISSING'}")
    print(f"Staging database: {'INITIALIZED' if init_staging_database() else 'FAILED'}")
    print("=" * 60)
    print("Starting web server...")
    print(f"Access the application at: http://localhost:{port}")
    print(f"Debug endpoint available at: http://localhost:{port}/api/debug")
    print(f"Database management at: http://localhost:{port}/api/database/")
    print(f"Staging API available at: http://localhost:{port}/api/staging/")
    print("=" * 60)

    # Run the Flask app
    app.run(debug=debug, host=host, port=port)

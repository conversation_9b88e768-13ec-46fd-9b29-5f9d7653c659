#!/usr/bin/env python3
"""
Quick test script to validate database connection functionality.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(__file__))

def test_database_manager():
    """Test the DatabaseConnectionManager directly."""
    try:
        # Import the necessary components
        from web_app import DatabaseConnectionManager, load_config
        
        print("🔧 Testing Database Connection Manager...")
        
        # Load configuration
        config = load_config()
        print(f"✅ Configuration loaded: {config['database_config']['connection_mode']}")
        
        # Initialize database manager
        db_manager = DatabaseConnectionManager(config['database_config'])
        print("✅ Database manager initialized")
        
        # Test local connection
        print("\n🧪 Testing local database connection...")
        success, message = db_manager.test_connection('local')
        print(f"   Result: {'✅ Success' if success else '❌ Failed'}")
        print(f"   Message: {message}")
        
        # Test remote connection (should fail quickly)
        print("\n🧪 Testing remote database connection...")
        success, message = db_manager.test_connection('remote')
        print(f"   Result: {'✅ Success' if success else '❌ Failed (Expected)'}")
        print(f"   Message: {message}")
        
        # Test status retrieval
        print("\n📊 Getting connection status...")
        status = db_manager.get_status()
        print(f"   Current mode: {status['current_mode']}")
        print(f"   Local connected: {status['local_status'].get('connected', 'Unknown')}")
        print(f"   Remote connected: {status['remote_status'].get('connected', 'Unknown')}")
        
        print("\n🎉 Database manager tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("🔧 QUICK DATABASE CONNECTION TEST")
    print(f"📅 Started at: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 60)
    
    success = test_database_manager()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULT")
    print("=" * 60)
    
    if success:
        print("✅ All tests passed! Database connection manager is working correctly.")
        print("\n🌐 Issues with the web API endpoints are likely due to:")
        print("   1. Server restart needed")
        print("   2. Import/module loading issues")
        print("   3. Flask application initialization problems")
        return True
    else:
        print("❌ Tests failed! Check the database connection manager implementation.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        sys.exit(1) 
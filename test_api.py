#!/usr/bin/env python3
"""
Simple script to test the database switch API endpoint
"""

import requests
import json

def test_database_switch():
    """Test switching database modes"""
    
    # Test switching to remote mode
    print("Testing database switch API...")
    
    try:
        # Test current status
        print("\n1. Getting current database status...")
        response = requests.get('http://localhost:5173/api/database/status')
        if response.status_code == 200:
            status = response.json()
            print(f"   Current mode: {status.get('status', {}).get('current_mode', 'unknown')}")
            print(f"   Local connected: {status.get('status', {}).get('local_status', {}).get('connected', False)}")
            print(f"   Remote connected: {status.get('status', {}).get('remote_status', {}).get('connected', False)}")
        else:
            print(f"   Failed to get status: {response.status_code}")
        
        # Test switching to remote mode
        print("\n2. Testing switch to remote mode...")
        switch_data = {"mode": "remote"}
        response = requests.post(
            'http://localhost:5173/api/database/switch-mode',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(switch_data),
            timeout=15
        )
        
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', False)}")
            print(f"   Message: {result.get('message', 'No message')}")
            print(f"   Current mode: {result.get('current_mode', 'unknown')}")
        else:
            print(f"   Error response: {response.text}")
        
        # Test switching to local mode
        print("\n3. Testing switch to local mode...")
        switch_data = {"mode": "local"}
        response = requests.post(
            'http://localhost:5173/api/database/switch-mode',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(switch_data),
            timeout=15
        )
        
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', False)}")
            print(f"   Message: {result.get('message', 'No message')}")
            print(f"   Current mode: {result.get('current_mode', 'unknown')}")
        else:
            print(f"   Error response: {response.text}")
            
        # Switch back to remote mode
        print("\n4. Switching back to remote mode...")
        switch_data = {"mode": "remote"}
        response = requests.post(
            'http://localhost:5173/api/database/switch-mode',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(switch_data),
            timeout=15
        )
        
        print(f"   Response status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Success: {result.get('success', False)}")
            print(f"   Message: {result.get('message', 'No message')}")
            print(f"   Current mode: {result.get('current_mode', 'unknown')}")
        else:
            print(f"   Error response: {response.text}")
            
    except Exception as e:
        print(f"Error testing API: {e}")

if __name__ == "__main__":
    test_database_switch()

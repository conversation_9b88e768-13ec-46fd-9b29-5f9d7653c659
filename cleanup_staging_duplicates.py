#!/usr/bin/env python3
"""
Standalone script to cleanup duplicate records in staging database.
This script can be run independently or called from the main application.
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_staging_db_connection():
    """Get a connection to the staging database."""
    staging_db_path = os.path.join('data', 'staging_attendance.db')
    if not os.path.exists(staging_db_path):
        logger.error(f"Staging database not found at: {staging_db_path}")
        return None
    return sqlite3.connect(staging_db_path)

def cleanup_duplicate_staging_records():
    """Clean up duplicate records in staging database, keeping the most recent ones."""
    try:
        conn = get_staging_db_connection()
        if not conn:
            return {'success': False, 'error': 'Could not connect to staging database'}
        
        cursor = conn.cursor()
        
        # Find duplicate records (same employee_id + date combination)
        cursor.execute('''
            SELECT employee_id, date, COUNT(*) as count, 
                   GROUP_CONCAT(id) as all_ids,
                   GROUP_CONCAT(created_at) as all_created_at
            FROM staging_attendance 
            GROUP BY employee_id, date 
            HAVING COUNT(*) > 1
            ORDER BY employee_id, date
        ''')
        
        duplicates = cursor.fetchall()
        
        if not duplicates:
            logger.info("No duplicate records found in staging database")
            conn.close()
            return {
                'success': True,
                'duplicates_found': 0, 
                'records_removed': 0,
                'message': 'No duplicates found'
            }
        
        logger.info(f"Found {len(duplicates)} sets of duplicate records")
        
        total_removed = 0
        
        for dup in duplicates:
            employee_id, date, count, all_ids, all_created_at = dup
            ids_list = all_ids.split(',')
            created_at_list = all_created_at.split(',')
            
            # Create list of (id, created_at) pairs and sort by created_at (most recent first)
            id_time_pairs = list(zip(ids_list, created_at_list))
            id_time_pairs.sort(key=lambda x: x[1], reverse=True)
            
            # Keep the most recent record, remove the rest
            keep_id = id_time_pairs[0][0]
            remove_ids = [pair[0] for pair in id_time_pairs[1:]]
            
            logger.info(f"For {employee_id} on {date}: keeping {keep_id}, removing {len(remove_ids)} duplicates")
            
            # Remove duplicate records
            for remove_id in remove_ids:
                cursor.execute('DELETE FROM staging_attendance WHERE id = ?', (remove_id,))
                total_removed += 1
        
        conn.commit()
        conn.close()
        
        logger.info(f"Cleanup completed: removed {total_removed} duplicate records from {len(duplicates)} duplicate sets")
        
        return {
            'success': True,
            'duplicates_found': len(duplicates),
            'records_removed': total_removed,
            'message': f'Successfully removed {total_removed} duplicate records'
        }
        
    except Exception as e:
        logger.error(f"Error during duplicate cleanup: {e}")
        return {
            'success': False,
            'duplicates_found': 0,
            'records_removed': 0,
            'error': str(e)
        }

def check_for_duplicates():
    """Check for duplicate records without removing them."""
    try:
        conn = get_staging_db_connection()
        if not conn:
            return {'success': False, 'error': 'Could not connect to staging database'}
        
        cursor = conn.cursor()
        
        # Find duplicate records
        cursor.execute('''
            SELECT employee_id, employee_name, date, COUNT(*) as count
            FROM staging_attendance 
            GROUP BY employee_id, date 
            HAVING COUNT(*) > 1
            ORDER BY employee_id, date
        ''')
        
        duplicates = cursor.fetchall()
        
        # Get total record count
        cursor.execute('SELECT COUNT(*) FROM staging_attendance')
        total_records = cursor.fetchone()[0]
        
        conn.close()
        
        total_duplicate_records = sum(dup[3] for dup in duplicates)
        
        return {
            'success': True,
            'total_records': total_records,
            'duplicate_sets': len(duplicates),
            'total_duplicate_records': total_duplicate_records,
            'records_that_would_be_removed': total_duplicate_records - len(duplicates),
            'has_duplicates': len(duplicates) > 0,
            'duplicates': duplicates
        }
        
    except Exception as e:
        logger.error(f"Error checking for duplicates: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Main function for standalone execution."""
    print("Staging Database Duplicate Cleanup Tool")
    print("=" * 40)
    
    # Check for duplicates first
    print("Checking for duplicate records...")
    check_result = check_for_duplicates()
    
    if not check_result['success']:
        print(f"Error checking for duplicates: {check_result['error']}")
        return 1
    
    if not check_result['has_duplicates']:
        print("No duplicate records found. Database is clean.")
        return 0
    
    print(f"Found {check_result['duplicate_sets']} sets of duplicate records")
    print(f"Total records: {check_result['total_records']}")
    print(f"Total duplicate records: {check_result['total_duplicate_records']}")
    print(f"Records that would be removed: {check_result['records_that_would_be_removed']}")
    
    # Ask for confirmation
    response = input("\nDo you want to cleanup these duplicates? (y/N): ").strip().lower()
    
    if response != 'y':
        print("Cleanup cancelled.")
        return 0
    
    # Perform cleanup
    print("\nPerforming cleanup...")
    cleanup_result = cleanup_duplicate_staging_records()
    
    if cleanup_result['success']:
        print(f"Cleanup completed successfully!")
        print(f"Removed {cleanup_result['records_removed']} duplicate records")
    else:
        print(f"Cleanup failed: {cleanup_result.get('error', 'Unknown error')}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

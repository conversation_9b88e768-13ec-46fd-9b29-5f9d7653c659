"""
Database analysis script to debug why no data is appearing.
This script will analyze the database structure and data to identify issues.
"""

import sys
import os
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../analyze_venus/src')))

from db_connection import DatabaseConnection

def analyze_database_structure():
    """Analyze the database structure and data."""
    
    print("=" * 80)
    print("DATABASE STRUCTURE ANALYSIS")
    print("=" * 80)
    
    db = DatabaseConnection()
    
    try:
        db.connect()
        print("✓ Database connection successful")
        
        # 1. Check if tables exist
        print("\n1. CHECKING TABLE EXISTENCE:")
        tables_to_check = [
            'HR_T_TAMachine_Summary',
            'HR_M_EmployeePI', 
            'HR_T_Overtime'
        ]
        
        for table in tables_to_check:
            query = f"""
            SELECT COUNT(*) as table_exists 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = '{table}'
            """
            result = db.execute_query(query)
            exists = result[0]['table_exists'] > 0 if result else False
            status = "✓ EXISTS" if exists else "✗ NOT FOUND"
            print(f"   {table}: {status}")
        
        # 2. Check HR_T_TAMachine_Summary structure and data
        print("\n2. ANALYZING HR_T_TAMachine_Summary:")
        
        # Check table structure
        structure_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'HR_T_TAMachine_Summary'
        ORDER BY ORDINAL_POSITION
        """
        
        try:
            columns = db.execute_query(structure_query)
            print("   Table structure:")
            for col in columns:
                print(f"     - {col['COLUMN_NAME']}: {col['DATA_TYPE']} ({'NULL' if col['IS_NULLABLE'] == 'YES' else 'NOT NULL'})")
        except Exception as e:
            print(f"   ✗ Error getting table structure: {e}")
        
        # Check total record count
        try:
            count_query = "SELECT COUNT(*) as total_records FROM HR_T_TAMachine_Summary"
            result = db.execute_query(count_query)
            total_records = result[0]['total_records'] if result else 0
            print(f"   Total records: {total_records}")
        except Exception as e:
            print(f"   ✗ Error counting records: {e}")
            return
        
        if total_records == 0:
            print("   ⚠ NO DATA FOUND in HR_T_TAMachine_Summary table!")
            return
        
        # Check date range of data
        try:
            date_range_query = """
            SELECT 
                MIN(TADate) as earliest_date,
                MAX(TADate) as latest_date,
                COUNT(DISTINCT TADate) as unique_dates
            FROM HR_T_TAMachine_Summary 
            WHERE TADate IS NOT NULL
            """
            result = db.execute_query(date_range_query)
            if result:
                data = result[0]
                print(f"   Date range: {data['earliest_date']} to {data['latest_date']}")
                print(f"   Unique dates: {data['unique_dates']}")
        except Exception as e:
            print(f"   ✗ Error getting date range: {e}")
        
        # Check business codes
        try:
            buscode_query = """
            SELECT BusCode, COUNT(*) as record_count
            FROM HR_T_TAMachine_Summary 
            GROUP BY BusCode
            ORDER BY record_count DESC
            """
            result = db.execute_query(buscode_query)
            print("   Business codes found:")
            for row in result[:10]:  # Show top 10
                print(f"     - {row['BusCode']}: {row['record_count']} records")
        except Exception as e:
            print(f"   ✗ Error getting business codes: {e}")
        
        # Check sample data
        try:
            sample_query = """
            SELECT TOP 5 
                BusCode, EmployeeID, UserDeviceName, TADate, TACheckIn, TACheckOut, Shift
            FROM HR_T_TAMachine_Summary 
            WHERE TADate IS NOT NULL
            ORDER BY TADate DESC
            """
            result = db.execute_query(sample_query)
            print("   Sample records:")
            for i, row in enumerate(result, 1):
                print(f"     {i}. {row['BusCode']} | {row['EmployeeID']} | {row['TADate']} | {row['TACheckIn']}-{row['TACheckOut']}")
        except Exception as e:
            print(f"   ✗ Error getting sample data: {e}")
        
        # 3. Test the get_available_months query directly
        print("\n3. TESTING get_available_months QUERY:")
        
        months_query = """
        SELECT 
            YEAR(t.TADate) as Year,
            MONTH(t.TADate) as Month,
            DATENAME(MONTH, t.TADate) as MonthName,
            COUNT(*) as RecordCount,
            COUNT(DISTINCT t.EmployeeID) as EmployeeCount,
            MIN(t.TADate) as FirstDate,
            MAX(t.TADate) as LastDate
        FROM HR_T_TAMachine_Summary t
        WHERE t.TADate IS NOT NULL
        GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
        ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
        """
        
        try:
            months_result = db.execute_query(months_query)
            print(f"   Available months query returned: {len(months_result)} months")
            
            if months_result:
                print("   Available months:")
                for month in months_result[:10]:  # Show first 10 months
                    print(f"     - {month['MonthName']} {month['Year']}: {month['RecordCount']} records, {month['EmployeeCount']} employees")
            else:
                print("   ⚠ No months returned by query!")
                
        except Exception as e:
            print(f"   ✗ Error executing months query: {e}")
        
        # 4. Test with specific business code
        print("\n4. TESTING WITH BUSINESS CODE 'PTRJ':")
        
        ptrj_query = """
        SELECT 
            YEAR(t.TADate) as Year,
            MONTH(t.TADate) as Month,
            DATENAME(MONTH, t.TADate) as MonthName,
            COUNT(*) as RecordCount,
            COUNT(DISTINCT t.EmployeeID) as EmployeeCount
        FROM HR_T_TAMachine_Summary t
        WHERE t.TADate IS NOT NULL AND t.BusCode = 'PTRJ'
        GROUP BY YEAR(t.TADate), MONTH(t.TADate), DATENAME(MONTH, t.TADate)
        ORDER BY YEAR(t.TADate) DESC, MONTH(t.TADate) DESC
        """
        
        try:
            ptrj_result = db.execute_query(ptrj_query)
            print(f"   PTRJ months query returned: {len(ptrj_result)} months")
            
            if ptrj_result:
                print("   PTRJ months:")
                for month in ptrj_result[:5]:
                    print(f"     - {month['MonthName']} {month['Year']}: {month['RecordCount']} records")
            else:
                print("   ⚠ No PTRJ data found!")
                
        except Exception as e:
            print(f"   ✗ Error executing PTRJ query: {e}")
        
        # 5. Check HR_M_EmployeePI table
        print("\n5. CHECKING HR_M_EmployeePI TABLE:")
        
        try:
            emp_count_query = "SELECT COUNT(*) as total_employees FROM HR_M_EmployeePI"
            result = db.execute_query(emp_count_query)
            emp_count = result[0]['total_employees'] if result else 0
            print(f"   Total employees: {emp_count}")
            
            if emp_count > 0:
                sample_emp_query = """
                SELECT TOP 5 EmployeeID, EmployeeName, BusCode 
                FROM HR_M_EmployeePI 
                ORDER BY EmployeeID
                """
                emp_result = db.execute_query(sample_emp_query)
                print("   Sample employees:")
                for emp in emp_result:
                    print(f"     - {emp['EmployeeID']}: {emp['EmployeeName']} ({emp['BusCode']})")
                    
        except Exception as e:
            print(f"   ✗ Error checking employees: {e}")
        
        # 6. Check HR_T_Overtime table
        print("\n6. CHECKING HR_T_Overtime TABLE:")
        
        try:
            ot_count_query = "SELECT COUNT(*) as total_overtime FROM HR_T_Overtime"
            result = db.execute_query(ot_count_query)
            ot_count = result[0]['total_overtime'] if result else 0
            print(f"   Total overtime records: {ot_count}")
            
        except Exception as e:
            print(f"   ✗ Error checking overtime: {e}")
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        print("\nPossible issues:")
        print("1. Database server is not running")
        print("2. Connection parameters are incorrect")
        print("3. Database name 'VenusHR14' doesn't exist")
        print("4. Authentication failed")
        
    finally:
        db.disconnect()

def test_attendance_reporter():
    """Test the AttendanceReporter class directly."""
    
    print("\n" + "=" * 80)
    print("TESTING ATTENDANCE REPORTER")
    print("=" * 80)
    
    try:
        from attendance_reporter import AttendanceReporter
        
        reporter = AttendanceReporter()
        
        # Test get_available_months without business code
        print("\n1. Testing get_available_months() without business code:")
        months = reporter.get_available_months()
        print(f"   Result: {len(months)} months found")
        
        if months:
            for month in months[:3]:
                print(f"   - {month}")
        a
        # Test get_available_months with PTRJ
        print("\n2. Testing get_available_months() with PTRJ:")
        ptrj_months = reporter.get_available_months("PTRJ")
        print(f"   Result: {len(ptrj_months)} months found")
        
        # Test get_employees_list
        print("\n3. Testing get_employees_list():")
        employees = reporter.get_employees_list("PTRJ")
        print(f"   Result: {len(employees)} employees found")
        
        # Test get_shifts_list
        print("\n4. Testing get_shifts_list():")
        shifts = reporter.get_shifts_list("PTRJ")
        print(f"   Result: {len(shifts)} shifts found")
        
    except Exception as e:
        print(f"✗ Error testing AttendanceReporter: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_database_structure()
    test_attendance_reporter()
    
    print("\n" + "=" * 80)
    print("ANALYSIS COMPLETE")
    print("=" * 80)
    print("\nIf no data was found, possible solutions:")
    print("1. Check if the database has attendance data")
    print("2. Verify the correct business code (might not be 'PTRJ')")
    print("3. Check if table names are correct")
    print("4. Verify database connection parameters")
    print("5. Check if data exists in the expected date range")

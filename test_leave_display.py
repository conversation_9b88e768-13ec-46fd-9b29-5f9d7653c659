#!/usr/bin/env python3
"""
Test script untuk memverifikasi implementasi leave data display
Test untuk memastikan:
1. Leave data di-fetch dengan benar dari HR_H_Leave table
2. Leave type codes (CT, H2, P1, P2, P3) ditampilkan di grid cells
3. ALFA status ditampilkan untuk absent tanpa cuti
4. CSS styling leave cells berfungsi
"""

import sys
import os
import requests
import json
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.attendance_reporter import AttendanceReporter

def test_leave_data_fetch():
    """Test fetching leave data from database"""
    print("🔍 Testing Leave Data Fetch...")
    
    try:
        reporter = AttendanceReporter()
        
        # Test leave data retrieval for June 2025
        start_date = "2025-06-01"
        end_date = "2025-06-30"
        bus_code = "PTRJ"
        
        print(f"📅 Fetching leave data for {start_date} to {end_date}")
        leave_data = reporter.get_leave_data(start_date, end_date, bus_code)
        
        print(f"✅ Retrieved {len(leave_data)} leave records")
        
        if leave_data:
            print("\n📋 Sample leave records:")
            for i, record in enumerate(leave_data[:5]):  # Show first 5 records
                print(f"  {i+1}. Employee: {record.get('EmployeeID')} - {record.get('EmployeeName')}")
                print(f"     Leave Type: {record.get('LeaveTypeCode')} - Date: {record.get('RefDate')}")
                print(f"     Description: {reporter.get_leave_type_description(record.get('LeaveTypeCode'))}")
                print()
        
        # Test leave type mapping
        print("🏷️  Testing leave type mappings:")
        test_codes = ['CT', 'H2', 'P1', 'P2', 'P3']
        for code in test_codes:
            description = reporter.get_leave_type_description(code)
            print(f"  {code} -> {description}")
        
        return len(leave_data) > 0
        
    except Exception as e:
        print(f"❌ Error testing leave data fetch: {str(e)}")
        return False

def test_monthly_grid_leave_integration():
    """Test monthly grid with leave integration"""
    print("\n📊 Testing Monthly Grid Leave Integration...")
    
    try:
        reporter = AttendanceReporter()
        
        # Get monthly grid for June 2025
        year = 2025
        month = 6
        bus_code = "PTRJ"
        
        print(f"📅 Getting monthly grid for {year}-{month:02d}")
        grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)
        
        if not grid_data or not grid_data.get('grid_data'):
            print("❌ No grid data received")
            return False
            
        print(f"✅ Grid data generated for {len(grid_data['grid_data'])} employees")
        
        # Analyze leave data in grid
        leave_found = 0
        alfa_found = 0
        total_cells = 0
        
        for employee in grid_data['grid_data']:
            employee_name = employee.get('EmployeeName', 'Unknown')
            employee_id = employee.get('EmployeeID', 'Unknown')
            
            for day in range(1, grid_data['days_in_month'] + 1):
                day_data = employee['days'].get(str(day))
                total_cells += 1
                
                if day_data:
                    status = day_data.get('status', 'unknown')
                    leave_data = day_data.get('leave_data')
                    
                    if status == 'on_leave' and leave_data:
                        leave_found += 1
                        leave_type = leave_data.get('leave_type_code', 'Unknown')
                        print(f"  📝 {employee_name} (Day {day}): LEAVE - {leave_type}")
                        
                    elif status == 'alfa':
                        alfa_found += 1
                        print(f"  🚨 {employee_name} (Day {day}): ALFA")
        
        print(f"\n📈 Summary:")
        print(f"  Total cells analyzed: {total_cells}")
        print(f"  Leave records found: {leave_found}")
        print(f"  ALFA records found: {alfa_found}")
        
        return leave_found > 0 or alfa_found > 0
        
    except Exception as e:
        print(f"❌ Error testing monthly grid: {str(e)}")
        return False

def test_api_integration():
    """Test API integration for leave data"""
    print("\n🌐 Testing API Integration...")
    
    try:
        base_url = "http://localhost:5173"
        
        # Test monthly grid API
        url = f"{base_url}/api/monthly-grid"
        params = {
            'year': 2025,
            'month': 6,
            'bus_code': 'PTRJ'
        }
        
        print(f"📡 Calling API: {url}")
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API call successful - Status: {response.status_code}")
            
            grid_data = data.get('data', {})
            if grid_data.get('grid_data'):
                print(f"📊 API returned data for {len(grid_data['grid_data'])} employees")
                
                # Check for leave data in API response
                leave_count = 0
                alfa_count = 0
                
                for employee in grid_data['grid_data'][:3]:  # Check first 3 employees
                    for day in range(1, min(8, grid_data['days_in_month'] + 1)):  # Check first week
                        day_data = employee['days'].get(str(day))
                        if day_data:
                            status = day_data.get('status', '')
                            if status == 'on_leave':
                                leave_count += 1
                            elif status == 'alfa':
                                alfa_count += 1
                
                print(f"  Leave entries in API: {leave_count}")
                print(f"  ALFA entries in API: {alfa_count}")
                
                return True
            else:
                print("⚠️  No grid data in API response")
                return False
        else:
            print(f"❌ API call failed - Status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API integration: {str(e)}")
        return False

def test_leave_data_structure():
    """Test the structure of leave data in grid"""
    print("\n🔍 Testing Leave Data Structure...")
    
    try:
        reporter = AttendanceReporter()
        
        # Get attendance data with leave integration
        start_date = "2025-06-01"
        end_date = "2025-06-10"  # First 10 days for testing
        bus_code = "PTRJ"
        
        print(f"📅 Getting attendance data with leave for {start_date} to {end_date}")
        attendance_data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        if not attendance_data:
            print("❌ No attendance data received")
            return False
            
        print(f"✅ Retrieved {len(attendance_data)} attendance records")
        
        # Analyze leave data structure
        leave_records = [record for record in attendance_data if record.get('LeaveData')]
        print(f"📋 Records with leave data: {len(leave_records)}")
        
        if leave_records:
            print("\n📝 Sample leave data structure:")
            sample = leave_records[0]
            leave_data = sample.get('LeaveData', {})
            for key, value in leave_data.items():
                print(f"  {key}: {value}")
        
        # Count different statuses
        status_counts = {}
        for record in attendance_data:
            status = record.get('Status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"\n📊 Status distribution:")
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
        
        return len(leave_records) > 0
        
    except Exception as e:
        print(f"❌ Error testing leave data structure: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Leave Data Display Integration Test")
    print("=" * 60)
    
    # Test 1: Leave data fetch
    test1 = test_leave_data_fetch()
    
    # Test 2: Leave data structure
    test2 = test_leave_data_structure()
    
    # Test 3: Monthly grid integration
    test3 = test_monthly_grid_leave_integration()
    
    # Test 4: API integration
    test4 = test_api_integration()
    
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    print(f"  Leave Data Fetch: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  Leave Data Structure: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  Monthly Grid Integration: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"  API Integration: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 All tests passed! Leave data integration is working correctly!")
        print("📝 Key features:")
        print("  • Leave data fetched from HR_H_Leave table")
        print("  • Leave type codes (CT, H2, P1, P2, P3) displayed in grid cells")
        print("  • ALFA status for absent employees without leave")
        print("  • Color coding for different leave types")
        print("  • API integration working")
        
        print("\n🌐 To test in browser:")
        print("  1. Open http://localhost:5173")
        print("  2. Select Juni 2025")
        print("  3. Look for leave type codes (CT, H2, P1, P2, P3) in grid cells")
        print("  4. Look for red ALFA cells for absent without leave")
        
    else:
        print("\n⚠️  Some tests failed - check implementation")

if __name__ == "__main__":
    main() 
# API Documentation - Enhanced Staging Data with PTRJ Integration

## Overview
Dokumentasi API untuk sistem staging data Venus yang telah disempurnakan dengan integrasi PTRJ Employee ID dan struktur JSON yang lebih efisien.

## Endpoints

### 1. `/api/staging/data` (GET) - Legacy Format
**Deskripsi**: Mengambil data staging dengan format tradisional (flat structure) dengan opsi optimasi struktur.

**Query Parameters**:
- `start_date` (optional): Tanggal mulai filter (format: YYYY-MM-DD)
- `end_date` (optional): Tanggal akhir filter (format: YYYY-MM-DD)
- `employee_id` (optional): Filter berdasarkan Employee ID Venus
- `status` (optional): Status record (default: 'staged')
- `limit` (optional): Jumlah maksimal record (default: 1000)
- `offset` (optional): Offset untuk pagination (default: 0)
- `optimize` (optional): Aktifkan struktur optimasi ('true'/'false', default: 'false')

**Response Format (optimize=false)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "dc15fb3b-be14-4f2c-8484-fbd64392d288",
      "employee_id": "PTRJ.241000039",
      "employee_name": "Dedy Burhansyah",
      "ptrj_employee_id": "PTRJ.241000039",
      "date": "2025-06-16",
      "day_of_week": "",
      "shift": "EFFLUENT _SHIFT1",
      "check_in": "18:54",
      "check_out": "",
      "regular_hours": 7.0,
      "overtime_hours": 3.5,
      "total_hours": 10.5,
      "task_code": "(OC7220) EFFLUENT TREATMENT PLANT",
      "station_code": "STN-ETP (STATION EFFLUENT TREATMENT PLANT)",
      "machine_code": "ETP00000 (LABOUR COST)",
      "expense_code": "L (LABOUR)",
      "raw_charge_job": "(OC7220) EFFLUENT TREATMENT PLANT / STN-ETP (STATION EFFLUENT TREATMENT PLANT) / ETP00000 (LABOUR COST) / L (LABOUR)",
      "leave_type_code": null,
      "leave_type_description": null,
      "leave_ref_number": null,
      "is_alfa": false,
      "is_on_leave": false,
      "status": "staged",
      "created_at": "2025-06-24 02:46:26",
      "updated_at": "2025-06-24 02:46:26",
      "source_record_id": "PTRJ.241000039_20250616",
      "notes": "Selective copy for 3 employees from 2025-06-12 to 2025-06-20",
      "transfer_status": "success"
    }
  ],
  "total_records": 100,
  "returned_records": 50,
  "total_employees": 25,
  "successfully_transferred": 50,
  "structure_optimized": false,
  "charge_job_enhancement": {
    "enabled": true,
    "records_enhanced": 150,
    "source": "Google Apps Script API"
  },
  "pagination": {
    "limit": 1000,
    "offset": 0,
    "has_more": false
  }
}
```

### 2. `/api/staging/data-grouped` (GET) - New Enhanced Format
**Deskripsi**: Mengambil data staging dengan struktur baru yang dikelompokkan berdasarkan karyawan untuk efisiensi yang lebih baik.

**Query Parameters**:
- `start_date` (optional): Tanggal mulai filter (format: YYYY-MM-DD)
- `end_date` (optional): Tanggal akhir filter (format: YYYY-MM-DD)
- `employee_id` (optional): Filter berdasarkan Employee ID Venus
- `status` (optional): Status record (default: 'staged')
- `limit` (optional): Jumlah maksimal record (default: 1000)
- `offset` (optional): Offset untuk pagination (default: 0)

**Response Format (New Structure)**:
```json
{
  "success": true,
  "data": [
    {
      "identitas_karyawan": {
        "employee_id_venus": "PTRJ.241000039",
        "employee_id_ptrj": "PTRJ.241000039",
        "employee_name": "Dedy Burhansyah",
        "task_code": "(OC7220) EFFLUENT TREATMENT PLANT",
        "station_code": "STN-ETP (STATION EFFLUENT TREATMENT PLANT)",
        "machine_code": "ETP00000 (LABOUR COST)",
        "expense_code": "L (LABOUR)",
        "raw_charge_job": "(OC7220) EFFLUENT TREATMENT PLANT / STN-ETP (STATION EFFLUENT TREATMENT PLANT) / ETP00000 (LABOUR COST) / L (LABOUR)"
      },
      "data_presensi": [
        {
          "id": "dc15fb3b-be14-4f2c-8484-fbd64392d288",
          "date": "2025-06-16",
          "day_of_week": "",
          "shift": "EFFLUENT _SHIFT1",
          "check_in": "18:54",
          "check_out": "",
          "regular_hours": 7.0,
          "overtime_hours": 3.5,
          "total_hours": 10.5,
          "leave_type_code": null,
          "leave_type_description": null,
          "leave_ref_number": null,
          "is_alfa": false,
          "is_on_leave": false,
          "status": "staged",
          "created_at": "2025-06-24 02:46:26",
          "updated_at": "2025-06-24 02:46:26",
          "source_record_id": "PTRJ.241000039_20250616",
          "notes": "Selective copy for 3 employees from 2025-06-12 to 2025-06-20",
          "transfer_status": "success"
        },
        {
          "id": "another-record-id",
          "date": "2025-06-17",
          "day_of_week": "Tuesday",
          "shift": "EFFLUENT _SHIFT1",
          "check_in": "07:00",
          "check_out": "17:00",
          "regular_hours": 8.0,
          "overtime_hours": 2.0,
          "total_hours": 10.0,
          "leave_type_code": null,
          "leave_type_description": null,
          "leave_ref_number": null,
          "is_alfa": false,
          "is_on_leave": false,
          "status": "staged",
          "created_at": "2025-06-24 02:46:27",
          "updated_at": "2025-06-24 02:46:27",
          "source_record_id": "PTRJ.241000039_20250617",
          "notes": "Selective copy for 3 employees from 2025-06-12 to 2025-06-20",
          "transfer_status": "success"
        }
      ]
    }
  ],
  "total_records": 100,
  "returned_records": 50,
  "total_employees": 25,
  "successfully_transferred": 50,
  "structure_type": "grouped_by_employee",
  "charge_job_enhancement": {
    "enabled": true,
    "records_enhanced": 150,
    "source": "Google Apps Script API"
  },
  "ptrj_integration": {
    "enabled": true,
    "description": "PTRJ Employee ID included in identitas_karyawan"
  },
  "pagination": {
    "limit": 1000,
    "offset": 0,
    "has_more": false
  }
}
```

## Data Structure Properties

### identitas_karyawan (Employee Identity)
Berisi informasi identitas karyawan yang tidak berubah selain data presensi:

- **employee_id_venus** (string): Employee ID dari sistem Venus HR
- **employee_id_ptrj** (string): Employee ID dari sistem PTRJ Mill
- **employee_name** (string): Nama lengkap karyawan
- **task_code** (string): Kode tugas/pekerjaan
- **station_code** (string): Kode stasiun kerja
- **machine_code** (string): Kode mesin/peralatan
- **expense_code** (string): Kode biaya/expense
- **raw_charge_job** (string): Data charge job mentah lengkap

### data_presensi (Attendance Data)
Array berisi data aktivitas presensi karyawan:

- **id** (string): UUID unik untuk record
- **date** (string): Tanggal presensi (YYYY-MM-DD)
- **day_of_week** (string): Hari dalam seminggu
- **shift** (string): Nama shift kerja
- **check_in** (string): Waktu check-in (HH:MM)
- **check_out** (string): Waktu check-out (HH:MM)
- **regular_hours** (float): Jam kerja normal
- **overtime_hours** (float): Jam lembur
- **total_hours** (float): Total jam kerja
- **leave_type_code** (string|null): Kode jenis cuti
- **leave_type_description** (string|null): Deskripsi jenis cuti
- **leave_ref_number** (string|null): Nomor referensi cuti
- **is_alfa** (boolean): Status alfa (tidak masuk tanpa keterangan)
- **is_on_leave** (boolean): Status cuti
- **status** (string): Status record ('staged', 'pending', dll)
- **created_at** (string): Timestamp pembuatan record
- **updated_at** (string): Timestamp update terakhir
- **source_record_id** (string): ID record sumber
- **notes** (string): Catatan tambahan
- **transfer_status** (string): Status transfer ('success', 'pending')

## Features Enhancement

### 1. PTRJ Employee ID Integration
- Sistem sekarang menampilkan kedua Employee ID (Venus dan PTRJ)
- Fuzzy matching dengan 5 strategi untuk mencocokkan nama karyawan
- Fallback otomatis jika tidak ada match yang ditemukan

### 2. Optimized JSON Structure
- **Struktur Lama**: Data berulang untuk setiap record presensi
- **Struktur Baru**: Identitas karyawan dipisah dari data presensi
- **Keuntungan**: Mengurangi redundansi data hingga 40-60%

### 3. Comprehensive Data Coverage
- Semua data yang ditampilkan di grid cell tersedia di API
- Integrasi lengkap dengan charge job data
- Data leave/cuti terintegrasi penuh
- Status ALFA dan partial attendance

### 4. Enhanced Error Handling
- Logging operasi yang komprehensif
- Fallback mechanism untuk charge job data
- Graceful degradation jika service eksternal tidak tersedia

## Usage Examples

### Mengambil Data dengan Struktur Baru
```javascript
// Fetch grouped staging data
const response = await fetch('/api/staging/data-grouped?start_date=2025-06-01&end_date=2025-06-30');
const data = await response.json();

// Process employee data
data.data.forEach(employee => {
    const identity = employee.identitas_karyawan;
    const attendance = employee.data_presensi;
    
    console.log(`Employee: ${identity.employee_name}`);
    console.log(`Venus ID: ${identity.employee_id_venus}`);
    console.log(`PTRJ ID: ${identity.employee_id_ptrj}`);
    console.log(`Attendance records: ${attendance.length}`);
    
    attendance.forEach(record => {
        console.log(`Date: ${record.date}, Hours: ${record.total_hours}`);
    });
});
```

### Mengambil Data dengan Format Legacy
```javascript
// Fetch traditional flat structure
const response = await fetch('/api/staging/data?start_date=2025-06-01&end_date=2025-06-30');
const data = await response.json();

// Process flat records
data.data.forEach(record => {
    console.log(`${record.employee_name} - ${record.date}: ${record.total_hours} hours`);
    console.log(`PTRJ ID: ${record.ptrj_employee_id}`);
});
```

## Performance Improvements

### Data Efficiency
- **Struktur Lama**: ~3.2KB per karyawan (5 hari kerja)
- **Struktur Baru**: ~1.8KB per karyawan (5 hari kerja)
- **Penghematan**: ~44% ukuran data

### Network Optimization
- Mengurangi bandwidth usage untuk aplikasi mobile
- Faster parsing di frontend
- Lebih efisien untuk large dataset

### Database Integration
- Query yang dioptimalkan dengan PTRJ Employee ID
- Index yang tepat untuk performa maksimal
- Caching strategy untuk charge job data

## Migration Guide

### Dari Struktur Lama ke Baru
1. **Backward Compatibility**: Endpoint lama tetap tersedia
2. **Gradual Migration**: Gunakan endpoint baru untuk fitur baru
3. **Data Mapping**: Panduan mapping dari flat ke grouped structure

### Frontend Adaptation
```javascript
// Old structure access
const employeeName = record.employee_name;
const totalHours = record.total_hours;

// New structure access
const employeeName = employee.identitas_karyawan.employee_name;
const totalHours = employee.data_presensi[0].total_hours;
```

## Error Codes

- **200**: Success
- **400**: Bad Request (parameter tidak valid)
- **404**: Data tidak ditemukan
- **500**: Internal Server Error
- **503**: Service Unavailable (Google Apps Script timeout)

## Rate Limiting
- Maximum 100 requests per minute per IP
- Pagination recommended untuk dataset besar
- Gunakan parameter filter untuk mengurangi load

## Support
Untuk pertanyaan atau masalah terkait API ini, silakan hubungi tim development atau buat issue di repository project. 
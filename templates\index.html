<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Rekap <PERSON><PERSON> - VenusHR</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        /* <PERSON><PERSON> and <PERSON><PERSON> */
        .company-logo {
            max-height: 50px;
            width: auto;
            margin-right: 15px;
        }
        
        .header-brand {
            display: flex;
            align-items: center;
        }
        
        .brand-text h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
        }
        
        .brand-tagline {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
            color: white;
        }
        
        .header-card {
            background: linear-gradient(135deg, #2c5aa0 0%, #3d6db0 50%, #4e7ec0 100%);
            border: none;
            box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .summary-card {
            border-left: 4px solid #667eea;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-export:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
        .month-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }
        .month-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25);
            border-color: #667eea;
        }
        .month-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .month-card .btn-hover-indicator {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .month-card:hover .btn-hover-indicator {
            opacity: 1;
        }
        .loading-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 50%;
            z-index: 10;
        }
        .month-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.9em;
        }

        /* Grid table styling */
        .table-container {
            overflow-x: auto;
            max-height: 600px;
            transition: all 0.3s ease;
        }
        
        /* Smooth transitions for sections */
        #monthlySummarySection {
            transition: all 0.5s ease;
        }
        
        /* Alert improvements */
        .alert {
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .attendance-grid {
            font-size: 0.85rem;
        }

        .attendance-grid th,
        .attendance-grid td {
            padding: 0.3rem !important;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid th:nth-child(2),
        .attendance-grid th:nth-child(3),
        .attendance-grid th:nth-child(4),
        .attendance-grid td:nth-child(1),
        .attendance-grid td:nth-child(2),
        .attendance-grid td:nth-child(3),
        .attendance-grid td:nth-child(4) {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 10;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid td:nth-child(1) {
            left: 0;
            min-width: 50px;
        }

        .attendance-grid th:nth-child(2),
        .attendance-grid td:nth-child(2) {
            left: 50px;
            min-width: 120px;
        }

        .attendance-grid th:nth-child(3),
        .attendance-grid td:nth-child(3) {
            left: 170px;
            min-width: 120px;
            text-align: center !important;
            color: #17a2b8 !important;
        }

        .attendance-grid th:nth-child(4),
        .attendance-grid td:nth-child(4) {
            left: 290px;
            min-width: 200px;
            text-align: left !important;
        }

        .attendance-grid .table-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
            font-weight: bold;
        }

        .attendance-grid .table-danger {
            background-color: #f8d7da !important;
            color: #842029;
            font-weight: bold;
        }

        .attendance-grid .table-warning {
            background-color: #fff3cd !important;
            color: #664d03;
            font-weight: bold;
        }

        .attendance-grid .table-secondary {
            background-color: #e2e3e5 !important;
            color: #41464b;
        }

        .attendance-grid .table-info {
            background-color: #d1ecf1 !important;
            color: #055160;
        }

        /* Working hours styling */
        .attendance-grid td {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        /* Color coding for different hour ranges */
        .hours-full {
            background-color: #d1e7dd !important;
            color: #0f5132;
            font-weight: bold;
        }

        /* Leave type styling */
        .hours-on-leave {
            background-color: #e8f5e8 !important;
            color: #2e7d32 !important;
            font-weight: bold;
        }

        /* Specific leave type colors */
        .leave-ct {
            background-color: #e3f2fd !important;
            color: #1976d2 !important;
        }

        .leave-h2 {
            background-color: #fce4ec !important;
            color: #c2185b !important;
        }

        .leave-p1 {
            background-color: #f3e5f5 !important;
            color: #7b1fa2 !important;
        }

        .leave-p2 {
            background-color: #e8f5e8 !important;
            color: #388e3c !important;
        }

        .leave-p3 {
            background-color: #fff3e0 !important;
            color: #f57c00 !important;
        }

        .leave-cb {
            background-color: #e1f5fe !important;
            color: #0277bd !important;
        }

        .leave-s {
            background-color: #ffebee !important;
            color: #d32f2f !important;
        }

        .leave-i {
            background-color: #f9fbe7 !important;
            color: #689f38 !important;
        }

        /* ALFA styling */
        .hours-alfa {
            background-color: #ffcdd2 !important;
            color: #d32f2f !important;
            font-weight: bold;
            border: 2px solid #d32f2f !important;
        }

        /* HR_T_Absence styling - New absence types from HR_T_Absence table */
        .absence-unpaid-alfa {
            background-color: #ffcdd2 !important; /* Red background for unpaid leave (ALFA) */
            color: #d32f2f !important;
            font-weight: bold;
            border: 2px solid #f44336 !important;
        }

        .absence-other {
            background-color: #fff3e0 !important; /* Light orange for other absence types */
            color: #e65100 !important;
            font-weight: bold;
            border: 2px solid #ff9800 !important;
        }

        /* Specific absence type styling */
        .absence-sick {
            background-color: #ffebee !important; /* Light red for sick */
            color: #c62828 !important;
            font-weight: bold;
            border: 2px solid #f44336 !important;
        }

        .absence-duty {
            background-color: #e8eaf6 !important; /* Light indigo for duty */
            color: #3f51b5 !important;
            font-weight: bold;
            border: 2px solid #3f51b5 !important;
        }

        .absence-menstrual {
            background-color: #fce4ec !important; /* Light pink for menstrual leave */
            color: #ad1457 !important;
            font-weight: bold;
            border: 2px solid #e91e63 !important;
        }

        .absence-additional {
            background-color: #f3e5f5 !important; /* Light purple for additional leave */
            color: #6a1b9a !important;
            font-weight: bold;
            border: 2px solid #9c27b0 !important;
        }

        /* OFF styling */
        .hours-off {
            background-color: #f5f5f5 !important;
            color: #757575 !important;
        }

        .hours-partial {
            background-color: #f8d7da !important;
            color: #842029;
            font-weight: bold;
        }

        .hours-overtime {
            background-color: #fff3cd !important;
            color: #664d03;
            font-weight: bold;
        }

        .hours-off {
            background-color: #d1ecf1 !important;
            color: #055160;
            font-style: italic;
        }

        .hours-absent {
            background-color: #f8f9fa !important;
            color: #6c757d;
        }

        /* Enhanced visual indicators for incomplete records */
        .hours-check-in-only,
        .hours-partial-check-in-only {
            background-color: #bbdefb !important; /* Blue background */
            color: #1976d2 !important;
            font-weight: bold;
            border: 2px solid #2196f3 !important;
        }

        .hours-check-out-only,
        .hours-partial-check-out-only {
            background-color: #bbdefb !important; /* Blue background - changed to match check-in only */
            color: #1976d2 !important;
            font-weight: bold;
            border: 2px solid #2196f3 !important;
        }

        /* Pink background for needs verification (original logic) */
        .hours-needs-verification {
            background-color: #f8bbd9 !important; /* Pink background */
            color: #c2185b !important;
            font-weight: bold;
            border: 2px solid #e91e63 !important;
        }

        /* Leave type styling */
        .hours-on-leave {
            background-color: #e8f5e8 !important; /* Light green for leave */
            color: #2e7d32 !important;
            font-weight: bold;
            border: 2px solid #4caf50 !important;
        }

        /* ALFA status styling */
        .hours-alfa {
            background-color: #ffcdd2 !important; /* Red background for ALFA */
            color: #d32f2f !important;
            font-weight: bold;
            border: 2px solid #f44336 !important;
        }

        /* Specific leave type colors */
        .hours-on-leave.leave-ct {
            background-color: #e3f2fd !important; /* Light blue for CUTI */
            color: #1976d2 !important;
            border-color: #2196f3 !important;
        }

        .hours-on-leave.leave-h2 {
            background-color: #fce4ec !important; /* Light pink for HAMIL/MELAHIRKAN */
            color: #c2185b !important;
            border-color: #e91e63 !important;
        }

        .hours-on-leave.leave-p1 {
            background-color: #f3e5f5 !important; /* Light purple for KELUARGA MENINGGAL */
            color: #7b1fa2 !important;
            border-color: #9c27b0 !important;
        }

        .hours-on-leave.leave-p2 {
            background-color: #e8f5e8 !important; /* Light green for IZIN MENIKAHKAN */
            color: #388e3c !important;
            border-color: #4caf50 !important;
        }

        .hours-on-leave.leave-p3 {
            background-color: #fff3e0 !important; /* Light orange for CUTI MENIKAH */
            color: #f57c00 !important;
            border-color: #ff9800 !important;
        }

        /* Enhanced color scheme for complete records */
        .hours-dark-green {
            background-color: #2e7d32 !important; /* Dark green: >7 hours with complete record */
            color: #ffffff !important;
            font-weight: bold;
        }

        .hours-light-green {
            background-color: #c8e6c9 !important; /* Light green: normal hours, no overtime */
            color: #2e7d32 !important;
            font-weight: bold;
        }

        /* New status: Overtime-only hours (no normal work hours) */
        .hours-overtime-only {
            background-color: #fff3cd !important; /* Yellow background */
            color: #664d03 !important;
            font-weight: bold;
            border: 2px solid #ffc107 !important;
        }

        /* Enhanced attendance display styles */
        .hours-normal {
            background-color: #e8f5e8 !important; /* Light green for normal hours only */
            color: #2d5a2d !important;
            font-weight: bold;
        }

        .hours-normal-overtime {
            background-color: #d4edda !important; /* Green: normal + overtime, complete check-in/out */
            color: #155724 !important;
            font-weight: bold;
            border: 2px solid #28a745 !important;
        }

        .hours-overtime-only {
            background-color: #fff3cd !important; /* Yellow: overtime only, complete check-in/out */
            color: #664d03 !important;
            font-weight: bold;
            border: 2px solid #ffc107 !important;
        }

        .hours-incomplete {
            background-color: #cce5ff !important; /* Blue: incomplete check-in or check-out */
            color: #0056b3 !important;
            font-weight: bold;
            border: 2px solid #007bff !important;
        }

        .hours-needs-verification {
            background-color: #fce4ec !important; /* Pink: needs verification/crosscheck */
            color: #880e4f !important;
            font-weight: bold;
            border: 2px solid #e91e63 !important;
        }

        /* Data unavailable cells - Enhanced styling for better visibility */
        .hours-data-unavailable {
            background-color: #f8f9fa !important; /* Light gray background */
            color: #6c757d !important;
            font-weight: bold;
            border: 2px dashed #adb5bd !important;
            font-style: italic;
            text-align: center;
            position: relative;
            cursor: not-allowed !important; /* Indicate non-selectable */
        }

        .hours-data-unavailable:hover {
            background-color: #e9ecef !important;
            border-color: #6c757d !important;
        }

        /* Additional styling for data unavailable cells to prevent selection */
        .data-unavailable-cell {
            pointer-events: none; /* Prevent clicking */
            opacity: 0.7;
            user-select: none;
        }

        /* Override pointer events for data unavailable cells in sync mode */
        .monthly-grid-sync-active .data-unavailable-cell {
            pointer-events: none !important;
            cursor: not-allowed !important;
        }

        /* Add strikethrough pattern for data unavailable cells */
        .hours-data-unavailable::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 10%;
            right: 10%;
            height: 1px;
            background: repeating-linear-gradient(
                to right,
                #adb5bd,
                #adb5bd 4px,
                transparent 4px,
                transparent 8px
            );
            transform: translateY(-50%);
        }

        /* National Holiday Header Styling */
        .header-national-holiday {
            background-color: #dc3545 !important; /* Red background for national holidays */
            color: white !important;
            font-weight: bold;
            border: 2px solid #bb2d3b !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .header-national-holiday:hover {
            background-color: #bb2d3b !important;
            transform: scale(1.05);
            transition: all 0.2s ease;
        }

        .hours-overtime {
            background-color: #cce5ff !important; /* Blue for overtime (legacy) */
            color: #003d7a !important;
            font-weight: bold;
        }

        /* Sync mode styles */
        .sync-mode-active {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }

        .sync-mode-active .card-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .row-selected {
            background-color: #cce5ff !important;
        }

        .row-selected td {
            background-color: #cce5ff !important;
        }

        .sync-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }

        .btn-sync-active {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
            border: none;
            color: white;
        }

        /* Monthly Grid Sync Mode Styles */
        .monthly-grid-sync-active {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }

        .monthly-grid-sync-active .card-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .monthly-grid-row-selectable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .monthly-grid-row-selectable:hover {
            background-color: #f8f9fa !important;
        }

        .monthly-grid-row-selected {
            background-color: #cce5ff !important;
        }

        .monthly-grid-row-selected td {
            background-color: #cce5ff !important;
        }

        .monthly-grid-row-number {
            background-color: #6c757d;
            color: white;
            border-radius: 4px;
            padding: 2px 6px;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
        }

        .monthly-grid-row-number:hover {
            background-color: #495057;
            transform: scale(1.1);
        }

        .monthly-grid-row-number.selected {
            background-color: #0d6efd;
            box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
        }

        .monthly-grid-checkbox {
            width: 16px;
            height: 16px;
            margin: 0;
            cursor: pointer;
        }

        /* Force checkbox visibility */
        .monthly-grid-row-checkbox {
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            cursor: pointer !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            border: 2px solid #0d6efd !important;
            background-color: white !important;
            z-index: 10 !important;
        }

        .monthly-grid-row-checkbox:checked {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
        }

        #selectAllMonthlyGrid {
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            cursor: pointer !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            border: 2px solid #fff !important;
            background-color: white !important;
            z-index: 25 !important;
        }

        #selectAllMonthlyGrid:checked {
            background-color: #ffc107 !important;
            border-color: #ffc107 !important;
        }

        /* Staging styles */
        .staging-mode-active {
            background-color: #cff4fc !important;
            border: 2px solid #0dcaf0 !important;
        }

        .staging-mode-active .card-header {
            background: linear-gradient(135deg, #0dcaf0 0%, #0a58ca 100%) !important;
        }

        .staging-row-selectable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .staging-row-selectable:hover {
            background-color: #f8f9fa !important;
        }

        .staging-row-selected {
            background-color: #cff4fc !important;
        }

        .staging-row-selected td {
            background-color: #cff4fc !important;
        }

        .staging-record-status-staged {
            background-color: #e2e3e5 !important;
            color: #41464b;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .staging-record-status-ready_for_upload {
            background-color: #d1e7dd !important;
            color: #0f5132;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .staging-record-status-error {
            background-color: #f8d7da !important;
            color: #842029;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .btn-staging-edit {
            background-color: #0dcaf0;
            border: none;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .btn-staging-edit:hover {
            background-color: #0a58ca;
            color: white;
        }

        .btn-staging-delete {
            background-color: #dc3545;
            border: none;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .btn-staging-delete:hover {
            background-color: #bb2d3b;
            color: white;
        }

        .staging-table-container {
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
        }

        /* Attendance selection styles */
        .attendance-row-selectable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .attendance-row-selectable:hover {
            background-color: #f8f9fa !important;
        }

        .attendance-row-checkbox {
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            cursor: pointer !important;
        }

        .attendance-row-checkbox:checked {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
        }

        #selectAllAttendance {
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            cursor: pointer !important;
        }

        #selectAllAttendance:checked {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
        }

        /* Transfer controls styling */
        #stagingTransferControls .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        #stagingTransferControls .alert {
            margin-bottom: 0;
        }

        #stagingTable {
            font-size: 0.85rem;
        }

        #stagingTable th,
        #stagingTable td {
            padding: 0.3rem !important;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
            white-space: nowrap;
        }

        #stagingTable th:nth-child(1),
        #stagingTable th:nth-child(2),
        #stagingTable th:nth-child(3),
        #stagingTable th:nth-child(4),
        #stagingTable td:nth-child(1),
        #stagingTable td:nth-child(2),
        #stagingTable td:nth-child(3),
        #stagingTable td:nth-child(4) {
            position: sticky;
            background-color: #f8f9fa;
            z-index: 15;
        }

        #stagingTable th:nth-child(3),
        #stagingTable th:nth-child(4),
        #stagingTable td:nth-child(3),
        #stagingTable td:nth-child(4) {
            text-align: left !important;
        }

        /* Transfer Status Indicators */
        .transfer-status-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .transfer-status-pending {
            background-color: #fff3cd !important;
            color: #664d03;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .transfer-status-failed {
            background-color: #f8d7da !important;
            color: #842029;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        /* Staging Row Numbers */
        .staging-row-number {
            background-color: #6c757d;
            color: white;
            border-radius: 4px;
            padding: 2px 6px;
            font-weight: bold;
            font-size: 0.8rem;
        }

        /* Log History Styling */
        #stagingLogsTable {
            font-size: 0.85rem;
        }

        #stagingLogsTable th,
        #stagingLogsTable td {
            padding: 0.5rem !important;
            vertical-align: middle;
        }

        .log-operation-read {
            background-color: #cff4fc !important;
            color: #055160;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .log-operation-insert,
        .log-operation-bulk_insert {
            background-color: #d1e7dd !important;
            color: #0f5132;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .log-operation-update {
            background-color: #fff3cd !important;
            color: #664d03;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .log-operation-delete {
            background-color: #f8d7da !important;
            color: #842029;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .log-status-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .log-status-failure {
            background-color: #f8d7da !important;
            color: #842029;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .log-status-warning {
            background-color: #fff3cd !important;
            color: #664d03;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .log-details-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
        }

        .log-details-cell:hover {
            overflow: visible;
            white-space: normal;
            word-wrap: break-word;
        }

        /* Log Statistics Styling */
        .operation-stat-badge {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .operation-stat-badge.read {
            background-color: #cff4fc;
            color: #055160;
        }

        .operation-stat-badge.insert {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .operation-stat-badge.update {
            background-color: #fff3cd;
            color: #664d03;
        }

        .operation-stat-badge.delete {
            background-color: #f8d7da;
            color: #842029;
        }

        /* Staging Configuration Styling */
        .staging-config-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
        }
        
        .staging-option {
            padding: 10px;
            border-radius: 6px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }
        
        .staging-option:hover {
            background-color: #f1f3f4;
            border-color: #dee2e6;
        }
        
        .staging-option input[type="radio"]:checked + label {
            color: #0d6efd;
            font-weight: 600;
        }
        
        .staging-option.local-staging input[type="radio"]:checked + label {
            color: #198754;
        }
        
        .staging-option.google-staging input[type="radio"]:checked + label {
            color: #0d6efd;
        }
        
        .staging-option input[type="radio"]:checked {
            accent-color: #0d6efd;
        }
        
        .staging-option.local-staging input[type="radio"]:checked {
            accent-color: #198754;
        }

        /* Database Connection Status Indicators */
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            position: relative;
        }

        .status-indicator.connected {
            background-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
            animation: pulse-success 2s infinite;
        }

        .status-indicator.disconnected {
            background-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
        }

        .status-indicator.unknown {
            background-color: #6c757d;
            box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.3);
        }

        .status-indicator.testing {
            background-color: #ffc107;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
            animation: pulse-warning 1s infinite;
        }

        @keyframes pulse-success {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        @keyframes pulse-warning {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 8px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }

        /* Health Indicators */
        .health-indicator {
            display: inline-block;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .health-indicator.healthy {
            color: #28a745;
            transform: scale(1.1);
        }

        .health-indicator.unhealthy {
            color: #dc3545;
            transform: scale(1.1);
        }

        .health-indicator.unknown {
            color: #6c757d;
        }

        /* Connection History Styles */
        .connection-history-entry {
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 6px;
            border-left: 4px solid;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .connection-history-entry.success {
            background-color: #d1e7dd;
            border-left-color: #28a745;
            color: #0f5132;
        }

        .connection-history-entry.failure {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #842029;
        }

        .connection-history-entry:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .connection-history-entry .timestamp {
            font-weight: bold;
            margin-right: 8px;
        }

        .connection-history-entry .mode {
            background-color: rgba(255,255,255,0.3);
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 8px;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.75rem;
        }

        .connection-history-entry .status {
            margin-right: 8px;
        }

        .connection-history-entry .message {
            font-style: italic;
        }

        /* Database Connection Card Modes */
        .database-connection-card.local-mode {
            border-left: 4px solid #198754;
        }

        .database-connection-card.remote-mode {
            border-left: 4px solid #0d6efd;
        }

        .database-connection-card.error-mode {
            border-left: 4px solid #dc3545;
        }

        /* Current Mode Indicator */
        .current-mode {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .current-mode.local {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .current-mode.remote {
            background-color: #d1ecf1;
            color: #055160;
        }

        /* Fallback Status Indicator */
        .fallback-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .fallback-status.enabled {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .fallback-status.disabled {
            background-color: #f8d7da;
            color: #842029;
        }

        /* Loading Animation for Connection Tests */
        .btn .spinner-border-sm {
            width: 0.875rem;
            height: 0.875rem;
        }

        /* Enhanced User Feedback Container */
        .user-feedback-container {
            margin-bottom: 1rem;
        }

        .user-feedback-container .alert {
            margin-bottom: 0.5rem;
        }

        /* Troubleshooting Hints Styling */
        .troubleshooting-hints .alert {
            border: none;
            border-left: 4px solid #17a2b8;
        }

        .troubleshooting-hints ul {
            margin-bottom: 0;
        }

        .troubleshooting-hints li {
            margin-bottom: 0.25rem;
        }

        /* Database Connection Toggle Section */
        .database-toggle-container {
            display: flex;
            align-items: center;
        }

        .toggle-label {
            margin-right: 0.5rem;
        }

        .connection-status-text {
            font-weight: bold;
        }
        
        /* Connection Status Color Coding */
        .connection-status-connected {
            color: #28a745 !important; /* Green for connected */
        }
        
        .connection-status-disconnected {
            color: #dc3545 !important; /* Red for disconnected */
        }
        
        .connection-status-unknown {
            color: #6c757d !important; /* Gray for unknown */
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card header-card">
                    <div class="card-header">
                        <div class="header-brand">
                            <img src="{{ url_for('static', filename='assets/logo.jpg') }}" alt="Company Logo" class="company-logo">
                            <div class="brand-text">
                                <h1>
                                    <i class="fas fa-clock me-2"></i>
                                    Sistem Rekap Absensi Karyawan
                                </h1>
                                <p class="brand-tagline">Laporan Lengkap Jam Kerja Normal & Lembur dari Mesin Absen VenusHR</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Connection Toggle Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card" id="databaseConnectionCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Status Koneksi Database
                        </h5>
                        <div class="d-flex align-items-center">
                            <!-- Database Selection Toggle Switch -->
                            <div class="database-toggle-container me-3">
                                <label class="toggle-label me-2">Lokal</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="databaseModeToggle">
                                </div>
                                <label class="toggle-label ms-2">Remote</label>
                            </div>
                            <!-- Scan Button -->
                            <button type="button" class="btn btn-primary btn-sm" id="scanConnectionsBtn">
                                <i class="fas fa-search me-1"></i>
                                <span id="scanLoading" class="spinner-border spinner-border-sm me-1" style="display: none;"></span>
                                Pindai
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- User Feedback Container -->
                        <div id="connectionFeedbackContainer"></div>
                        
                        <!-- Connection Status Row -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-2">
                                            <i class="fas fa-desktop me-2"></i>Database Lokal
                                        </h6>
                                        <div class="d-flex align-items-center">
                                            <div id="localStatusIndicator" class="status-indicator disconnected me-2"></div>
                                            <span id="localStatusText" class="fw-bold connection-status-text">Status Koneksi</span>
                                        </div>
                                        <small class="text-muted" id="localLastAttempt">Siap untuk dipindai</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-2">
                                            <i class="fas fa-cloud me-2"></i>Database Remote
                                        </h6>
                                        <div class="d-flex align-items-center">
                                            <div id="remoteStatusIndicator" class="status-indicator disconnected me-2"></div>
                                            <span id="remoteStatusText" class="fw-bold connection-status-text">Status Koneksi</span>
                                        </div>
                                        <small class="text-muted" id="remoteLastAttempt">Siap untuk dipindai</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Info Row -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info mb-0" id="databaseConnectionInfo">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span id="connectionInfoText">Gunakan toggle untuk memilih mode database dan klik "Pindai" untuk menguji konektivitas ke semua database yang dikonfigurasi.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly" type="button" role="tab">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Laporan Bulanan
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#custom" type="button" role="tab">
                            <i class="fas fa-filter me-2"></i>
                            Rentang Tanggal Kustom
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="staging-tab" data-bs-toggle="tab" data-bs-target="#staging" type="button" role="tab">
                            <i class="fas fa-upload me-2"></i>
                            Data Staging
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="reportTabsContent">
            <!-- Monthly Reports Tab -->
            <div class="tab-pane fade show active" id="monthly" role="tabpanel">
                <!-- Available Months Grid -->
                <div class="row mb-4" id="monthsGrid">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    Bulan Tersedia (Klik untuk Melihat Laporan)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row" id="monthsContainer">
                                    <div class="col-12 text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Memuat bulan yang tersedia...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Summary Section -->
                <div class="row mb-4" id="monthlySummarySection" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0" id="monthlyReportTitle">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Ringkasan Bulanan
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Karyawan</h6>
                                                <h4 class="text-primary" id="monthlyTotalEmployees">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Data Absen</h6>
                                                <h4 class="text-info" id="monthlyTotalRecords">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Hari Kerja</h6>
                                                <h4 class="text-success" id="monthlyWorkingDays">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Jam Normal</h6>
                                                <h4 class="text-success" id="monthlyRegularHours">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Jam Lembur</h6>
                                                <h4 class="text-warning" id="monthlyOvertimeHours">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <button class="btn btn-export btn-sm" id="exportMonthlyBtn">
                                                    <i class="fas fa-download me-1"></i>
                                                    Ekspor
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Date Range Tab -->
            <div class="tab-pane fade" id="custom" role="tabpanel">
                <!-- Custom Filters Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filter-section">
                            <h5 class="mb-3">
                                <i class="fas fa-filter me-2"></i>
                                Filter Rentang Tanggal Kustom
                            </h5>
                            <form id="filterForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="startDate" class="form-label">Tanggal Mulai</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="endDate" class="form-label">Tanggal Akhir</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="busCode" class="form-label">Kode Bisnis</label>
                                        <input type="text" class="form-control" id="busCode" placeholder="contoh: PTRJ">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="employeeSelect" class="form-label">Karyawan</label>
                                        <select class="form-select" id="employeeSelect">
                                            <option value="">Semua Karyawan</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="shiftSelect" class="form-label">Shift</label>
                                        <select class="form-select" id="shiftSelect">
                                            <option value="">Semua Shift</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-8">
                                        <label for="syncUrl" class="form-label">Google Apps Script URL (for sync)</label>
                                        <input type="url" class="form-control" id="syncUrl" placeholder="https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec"
                                               value="https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec">
                                        <small class="form-text text-muted">Enter your Google Apps Script deployment URL for syncing data</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableSync">
                                            <label class="form-check-label" for="enableSync">
                                                Enable sync functionality
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search me-1"></i>
                                            Generate Report
                                        </button>
                                        <button type="button" class="btn btn-export me-2" id="exportBtn">
                                            <i class="fas fa-download me-1"></i>
                                            Export to Excel
                                        </button>
                                        <button type="button" class="btn btn-info me-2" id="exportJsonBtn">
                                            <i class="fas fa-file-code me-1"></i>
                                            Export to JSON
                                        </button>
                                        <button type="button" class="btn btn-warning me-2" id="toggleSyncMode">
                                            <i class="fas fa-sync me-1"></i>
                                            Sync Mode
                                        </button>
                                        <button type="button" class="btn btn-success me-2" id="syncToSheetBtn" style="display: none;">
                                            <i class="fas fa-cloud-upload-alt me-1"></i>
                                            Sync to Spreadsheet
                                        </button>
                                        <button type="button" class="btn btn-secondary" id="clearBtn">
                                            <i class="fas fa-times me-1"></i>
                                            Clear Filters
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Custom Summary Cards -->
                <div class="row mb-4" id="summarySection" style="display: none;">
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Employees</h5>
                                <h3 class="text-primary" id="totalEmployees">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Records</h5>
                                <h3 class="text-info" id="totalRecords">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Regular Hours</h5>
                                <h3 class="text-success" id="totalRegularHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Overtime Hours</h5>
                                <h3 class="text-warning" id="totalOvertimeHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Avg Regular</h5>
                                <h3 class="text-success" id="avgRegularHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Avg Overtime</h5>
                                <h3 class="text-warning" id="avgOvertimeHours">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Staging Tab -->
            <div class="tab-pane fade" id="staging" role="tabpanel">
                <!-- Staging Mode Toggle -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filter-section">
                            <h5 class="mb-3">
                                <i class="fas fa-upload me-2"></i>
                                Staging Environment - Local Sync Mode
                            </h5>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Local Sync Staging Mode Active:</strong> Data will be stored locally in staging database instead of being uploaded to Google Apps Script.
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="stagingModeToggle" checked>
                                        <label class="form-check-label" for="stagingModeToggle">
                                            <strong>Local Sync Staging</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staging Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    Staging Operations
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Basic Move to Staging (Legacy) -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <h6 class="text-muted">
                                            <i class="fas fa-calendar-alt me-2"></i>
                                            Bulk Date Range Transfer (Legacy)
                                        </h6>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="stagingStartDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="stagingStartDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="stagingEndDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="stagingEndDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="stagingBusCode" class="form-label">Business Code</label>
                                        <input type="text" class="form-control" id="stagingBusCode" placeholder="e.g., PTRJ" value="PTRJ">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="button" class="btn btn-primary" id="moveToStagingBtn">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                Move to Staging
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <small class="text-muted">Select date range and click "Move to Staging" to transfer attendance records to staging area for review and editing.</small>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Enhanced Selective Copy -->
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <h6 class="text-primary">
                                            <i class="fas fa-users me-2"></i>
                                            Selective Employee Data Copy (Enhanced)
                                        </h6>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="selectiveEmployees" class="form-label">
                                            <i class="fas fa-user-check me-1"></i>
                                            Select Employees
                                        </label>
                                        <select class="form-control" id="selectiveEmployees" multiple size="8">
                                            <option value="" disabled>Loading employees...</option>
                                        </select>
                                        <small class="text-muted">Hold Ctrl/Cmd to select multiple employees</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label for="selectiveStartDate" class="form-label">Start Date</label>
                                                <input type="date" class="form-control" id="selectiveStartDate">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="selectiveEndDate" class="form-label">End Date</label>
                                                <input type="date" class="form-control" id="selectiveEndDate">
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <label for="selectiveBusCode" class="form-label">Business Code</label>
                                                <input type="text" class="form-control" id="selectiveBusCode" placeholder="e.g., PTRJ" value="PTRJ">
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-md-12">
                                                <div class="d-grid">
                                                    <button type="button" class="btn btn-success" id="selectiveCopyBtn">
                                                        <i class="fas fa-copy me-1"></i>
                                                        Copy Selected Data
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-md-12">
                                                <small class="text-muted">
                                                    <span id="selectedEmployeeCount">0</span> employees selected
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="alert alert-info mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>Selective Copy:</strong> Choose specific employees and date range to copy only their attendance data to staging. This provides more control than bulk date range transfers.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staging Statistics -->
                <div class="row mb-4" id="stagingStatsSection">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Staging Statistics
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Total Records</h6>
                                                <h4 class="text-primary" id="stagingTotalRecords">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Staged</h6>
                                                <h4 class="text-info" id="stagingStaged">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Ready for Upload</h6>
                                                <h4 class="text-success" id="stagingReady">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Date Range</h6>
                                                <small class="text-muted" id="stagingDateRange">No data</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <button class="btn btn-success btn-sm" id="refreshStagingBtn">
                                                    <i class="fas fa-sync me-1"></i>
                                                    Refresh
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <button class="btn btn-warning btn-sm" id="prepareStagingUploadBtn">
                                                    <i class="fas fa-cloud-upload-alt me-1"></i>
                                                    Prepare Upload
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-center">
                                            <button class="btn btn-warning btn-sm me-2" id="checkDuplicatesBtn">
                                                <i class="fas fa-search me-1"></i>
                                                Check Duplicates
                                            </button>
                                            <button class="btn btn-info btn-sm me-2" id="cleanupDuplicatesBtn">
                                                <i class="fas fa-broom me-1"></i>
                                                Cleanup Duplicates
                                            </button>
                                            <button class="btn btn-danger btn-sm me-2" id="deleteAllStagingBtn">
                                                <i class="fas fa-trash-alt me-1"></i>
                                                Delete All Staging Data
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" id="loadEmployeesBtn">
                                                <i class="fas fa-users me-1"></i>
                                                Load Employees
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staging Data Grid -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Staging Data Grid
                                    <span class="badge bg-info ms-2" id="stagingRecordCount">0 of 0 records transferred</span>
                                </h5>
                                <div>
                                    <button type="button" class="btn btn-success btn-sm me-2" id="addStagingRecordBtn">
                                        <i class="fas fa-plus me-1"></i>
                                        Add Record
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" id="deleteStagingSelectedBtn" disabled>
                                        <i class="fas fa-trash me-1"></i>
                                        Delete Selected
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="staging-table-container">
                                    <table class="table table-striped table-hover attendance-grid" id="stagingTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="min-width: 50px; position: sticky; left: 0; background: #212529; z-index: 20; text-align: center;">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="selectAllStaging">
                                                        <label class="form-check-label text-white" for="selectAllStaging" style="font-size: 0.8rem;">All</label>
                                                    </div>
                                                </th>
                                                <th style="min-width: 50px; position: sticky; left: 50px; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">No</th>
                                                <th style="min-width: 120px; position: sticky; left: 100px; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee ID</th>
                                                <th style="min-width: 120px; position: sticky; left: 220px; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem; color: #17a2b8;">PTRJ Employee ID</th>
                                                <th style="min-width: 200px; position: sticky; left: 340px; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee Name</th>
                                                <th style="min-width: 100px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #212529; z-index: 10; color: white;">Date</th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #212529; z-index: 10; color: white;">Day</th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #212529; z-index: 10; color: white;">Shift</th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #212529; z-index: 10; color: white;">Check In</th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #212529; z-index: 10; color: white;">Check Out</th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
                                                    <div>REG</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
                                                </th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #ffc107; z-index: 10; color: black;">
                                                    <div>OT</div>
                                                    <div style="font-size: 0.7rem; color: #6c757d;">Hours</div>
                                                </th>
                                                <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
                                                    <div>TOTAL</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
                                                </th>
                                                <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
                                                    <div>TASK</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
                                                </th>
                                                <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
                                                    <div>STATION</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
                                                </th>
                                                <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
                                                    <div>MACHINE</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
                                                </th>
                                                <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
                                                    <div>EXPENSE</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
                                                </th>
                                                <th style="min-width: 100px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #6c757d; z-index: 10; color: white;">
                                                    <div>STATUS</div>
                                                    <div style="font-size: 0.7rem; color: #adb5bd;">Transfer</div>
                                                </th>
                                                <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #dc3545; z-index: 10; color: white;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="stagingTableBody">
                                            <tr>
                                                <td colspan="19" class="text-center text-muted">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    No staging data available. Use "Move to Staging" to add records.
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Staging Log History Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    Staging Operations Log History
                                </h5>
                                <div>
                                    <button type="button" class="btn btn-outline-primary btn-sm me-2" id="refreshStagingLogsBtn">
                                        <i class="fas fa-sync me-1"></i>
                                        Refresh Logs
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" id="exportStagingLogsBtn">
                                        <i class="fas fa-download me-1"></i>
                                        Export Logs
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Log Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-2">
                                        <label for="logStartDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control form-control-sm" id="logStartDate">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="logEndDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control form-control-sm" id="logEndDate">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="logOperationType" class="form-label">Operation</label>
                                        <select class="form-select form-select-sm" id="logOperationType">
                                            <option value="">All Operations</option>
                                            <option value="READ">Read</option>
                                            <option value="INSERT">Insert</option>
                                            <option value="UPDATE">Update</option>
                                            <option value="DELETE">Delete</option>
                                            <option value="BULK_INSERT">Bulk Insert</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="logResultStatus" class="form-label">Status</label>
                                        <select class="form-select form-select-sm" id="logResultStatus">
                                            <option value="">All Status</option>
                                            <option value="success">Success</option>
                                            <option value="failure">Failure</option>
                                            <option value="warning">Warning</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="button" class="btn btn-primary btn-sm" id="filterStagingLogsBtn">
                                                <i class="fas fa-filter me-1"></i>
                                                Filter Logs
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            <button type="button" class="btn btn-secondary btn-sm" id="clearLogFiltersBtn">
                                                <i class="fas fa-times me-1"></i>
                                                Clear
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Statistics Summary -->
                                <div class="row mb-3" id="logStatsSection">
                                    <div class="col-md-12">
                                        <div class="alert alert-light">
                                            <h6 class="mb-2"><i class="fas fa-chart-bar me-2"></i>Operation Statistics</h6>
                                            <div id="logStatsSummary">
                                                <span class="text-muted">Load logs to view statistics</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Data Table -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sm" id="stagingLogsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Operation</th>
                                                <th>Table</th>
                                                <th>Details</th>
                                                <th>Records</th>
                                                <th>Status</th>
                                                <th>User/IP</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="stagingLogsTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    Click "Refresh Logs" to load operation history
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Log Pagination -->
                                <div class="row mt-3" id="logPaginationSection" style="display: none;">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center">
                                            <span class="text-muted" id="logPaginationInfo">Showing 0 of 0 log entries</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-end">
                                            <nav>
                                                <ul class="pagination pagination-sm mb-0">
                                                    <li class="page-item" id="logPrevPage">
                                                        <button class="page-link" type="button">
                                                            <i class="fas fa-chevron-left"></i>
                                                        </button>
                                                    </li>
                                                    <li class="page-item active" id="logCurrentPage">
                                                        <span class="page-link">1</span>
                                                    </li>
                                                    <li class="page-item" id="logNextPage">
                                                        <button class="page-link" type="button">
                                                            <i class="fas fa-chevron-right"></i>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading attendance data...</p>
        </div>

        <!-- Monthly Grid Sync Controls -->
        <div class="row mb-4" id="monthlyGridSyncControls" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Monthly Grid Sync Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>How to select rows for staging:</strong>
                            <ol class="mb-0 mt-2">
                                <li><strong>Load Monthly Grid:</strong> Select a month from the Monthly Reports tab</li>
                                <li><strong>Enable Sync:</strong> Check "Enable grid sync functionality" checkbox below</li>
                                <li><strong>Activate Mode:</strong> Click the "Sync Mode" button to see checkboxes</li>
                                <li><strong>Choose Type:</strong> Select "Local Staging" or "Google Apps Script Staging"</li>
                                <li><strong>Select Rows:</strong> Check individual employee rows in the grid</li>
                                <li><strong>Transfer:</strong> Click "Transfer Selected to Staging" button</li>
                            </ol>
                                                         <div class="mt-2 p-2 bg-light rounded">
                                 <small><strong>Troubleshooting:</strong> If checkboxes don't appear, make sure to load a monthly grid first, then enable sync, then click Sync Mode button. Use the 🔍 Debug button to check status.</small>
                             </div>
                             <div class="mt-2 p-2 bg-warning rounded">
                                 <small><strong>⚠️ Important:</strong> Grid harus dimuat TERLEBIH DAHULU dari tab "Monthly Reports", baru aktifkan Sync Mode!</small>
                             </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="monthlyGridSyncUrl" class="form-label">Google Apps Script URL (for sync)</label>
                                <input type="url" class="form-control" id="monthlyGridSyncUrl"
                                       placeholder="https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec"
                                       value="https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec">
                                <small class="form-text text-muted">Enter your Google Apps Script deployment URL for syncing grid data</small>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Staging Configuration</label>
                                <div class="staging-config-container">
                                    <div class="form-check staging-option local-staging">
                                        <input class="form-check-input" type="radio" name="stagingType" id="localStaging" value="local" checked>
                                        <label class="form-check-label" for="localStaging">
                                            <i class="fas fa-database me-1"></i>
                                            Local Staging
                                        </label>
                                    </div>
                                    <div class="form-check staging-option google-staging">
                                        <input class="form-check-input" type="radio" name="stagingType" id="googleAppsScriptStaging" value="google">
                                        <label class="form-check-label" for="googleAppsScriptStaging">
                                            <i class="fas fa-cloud me-1"></i>
                                            Google Apps Script Staging
                                        </label>
                                    </div>
                                </div>
                                <small class="form-text text-muted">Choose where to stage selected rows</small>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableMonthlyGridSync">
                                    <label class="form-check-label" for="enableMonthlyGridSync">
                                        Enable grid sync functionality
                                    </label>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-warning btn-sm" id="toggleMonthlyGridSyncMode">
                                        <i class="fas fa-sync me-1"></i>
                                        Sync Mode
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm ms-2" id="debugSyncMode" style="font-size: 0.7rem;">
                                        🔍 Debug
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3" id="monthlyGridSyncActions" style="display: none;">
                            <div class="col-md-8">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span id="monthlyGridSelectionInfo">No rows selected</span>
                                    <span class="ms-3" id="monthlyGridSelectionDetails"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" id="syncMonthlyGridToSheetBtn" disabled>
                                        <i class="fas fa-cloud-upload-alt me-1"></i>
                                        <span id="syncButtonText">Transfer Selected to Staging</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Staging Transfer Controls -->
        <div class="row mb-4" id="stagingTransferControls" style="display: none;">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            Transfer Selected Records to Staging
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="useFullMonthData" checked>
                                    <label class="form-check-label" for="useFullMonthData">
                                        <strong>Include Full Month Data</strong>
                                    </label>
                                    <small class="form-text text-muted d-block">
                                        Automatically include complete month data for selected employees
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4" id="customDateRangeSection" style="display: none;">
                                <label for="transferStartDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="transferStartDate">
                            </div>
                            <div class="col-md-4" id="customDateRangeSection2" style="display: none;">
                                <label for="transferEndDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="transferEndDate">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span id="selectedRecordsInfo">No records selected</span>
                                    <div class="mt-2" id="selectedRecordsDetails"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" id="transferSelectedToStagingBtn" disabled>
                                        <i class="fas fa-upload me-1"></i>
                                        Transfer to Staging
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Hasil Laporan Absensi
                        </h5>
                        <!-- Monthly Grid Sync Toggle -->
                        <div id="monthlyGridSyncToggleContainer" style="display: none;">
                            <button type="button" class="btn btn-warning btn-sm" id="toggleMonthlyGridSyncModeHeader">
                                <i class="fas fa-sync me-1"></i>
                                Mode Sinkronisasi
                            </button>
                        </div>

                        <!-- Staging Selection Toggle -->
                        <div id="stagingSelectionToggleContainer">
                            <button type="button" class="btn btn-primary btn-sm me-2" id="toggleStagingSelectionMode">
                                <i class="fas fa-check-square me-1"></i>
                                Selection Mode
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-striped table-hover" id="attendanceTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll" style="display: none;">
                                                <input class="form-check-input" type="checkbox" id="selectAllAttendance" style="display: none;">
                                                <label class="form-check-label" for="selectAll" id="selectAllLabel" style="display: none;">
                                                    Semua
                                                </label>
                                                <label class="form-check-label" for="selectAllAttendance" id="selectAllAttendanceLabel" style="display: none;">
                                                    All
                                                </label>
                                            </div>
                                        </th>
                                        <th>ID Karyawan</th>
                                        <th>Nama Karyawan</th>
                                        <th>Tanggal</th>
                                        <th>Hari</th>
                                        <th>Shift</th>
                                        <th>Jam Masuk</th>
                                        <th>Jam Keluar</th>
                                        <th>Jam Normal</th>
                                        <th>Jam Lembur</th>
                                        <th>Total Jam</th>
                                    </tr>
                                </thead>
                                <tbody id="attendanceTableBody">
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Silakan pilih rentang tanggal dan klik "Buat Laporan" untuk melihat data absensi
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- Color Legend for Attendance Grid -->
                            <div class="row mt-3" id="attendanceColorLegend" style="display: none;">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">
                                                <i class="fas fa-palette me-2"></i>
                                                Keterangan Warna Kehadiran
                                            </h6>
                                        </div>
                                        <div class="card-body p-3">
                                            <div class="row">
                                                <div class="col-md-3 col-sm-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge hours-normal-overtime me-2" style="min-width: 60px; font-size: 0.8em;">(7) | (2)</span>
                                                        <small>Hijau: Normal + Overtime (Lengkap)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-sm-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge hours-overtime-only me-2" style="min-width: 60px; font-size: 0.8em;">(0) | (3)</span>
                                                        <small>Kuning: Hanya Overtime (Lengkap)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-sm-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge hours-needs-verification me-2" style="min-width: 60px; font-size: 0.8em;">(7) | (0)</span>
                                                        <small>Pink: Perlu Crosscheck (Lupa Absen)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-sm-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <span class="badge hours-off me-2" style="min-width: 60px; font-size: 0.8em;">OFF</span>
                                                        <small>Abu: Hari Libur</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Configuration Modal -->
    <div class="modal fade" id="databaseConfigModal" tabindex="-1" aria-labelledby="databaseConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="databaseConfigModalLabel">
                        <i class="fas fa-database me-2"></i>
                        Database Configuration
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Connection Mode Selection -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">Connection Mode</h6>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="modalConnectionMode" id="modalLocalMode" value="local" checked>
                                <label class="form-check-label" for="modalLocalMode">
                                    <i class="fas fa-desktop me-1"></i>
                                    Local Database
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="modalConnectionMode" id="modalRemoteMode" value="remote">
                                <label class="form-check-label" for="modalRemoteMode">
                                    <i class="fas fa-cloud me-1"></i>
                                    Remote Database
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Local Database Configuration -->
                    <div id="modalLocalConfig" class="config-section">
                        <h6 class="fw-bold mb-3">Local Database Settings</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Local database configuration is managed through the config.json file. Default settings:
                            <ul class="mb-0 mt-2">
                                <li>Server: localhost</li>
                                <li>Port: 1433</li>
                                <li>Database: VenusHR14</li>
                                <li>Username: sa</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Remote Database Configuration -->
                    <div id="modalRemoteConfig" class="config-section" style="display: none;">
                        <h6 class="fw-bold mb-3">Remote Database Settings</h6>
                        <form id="modalRemoteConfigForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="modalRemoteServer" class="form-label">Server/IP Address <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="modalRemoteServer" placeholder="********" required>
                                    <div class="form-text">Enter the IP address or hostname of the remote SQL Server</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="modalRemotePort" class="form-label">Port <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="modalRemotePort" placeholder="1888" min="1" max="65535" required>
                                    <div class="form-text">SQL Server port (default: 1433, custom: 1888)</div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="modalRemoteDatabase" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="modalRemoteDatabase" placeholder="VenusHR14" value="VenusHR14">
                                    <div class="form-text">Name of the database to connect to</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="modalRemoteDriver" class="form-label">ODBC Driver</label>
                                    <select class="form-select" id="modalRemoteDriver">
                                        <option value="ODBC Driver 17 for SQL Server">ODBC Driver 17 for SQL Server</option>
                                        <option value="ODBC Driver 13 for SQL Server">ODBC Driver 13 for SQL Server</option>
                                        <option value="SQL Server Native Client 11.0">SQL Server Native Client 11.0</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="modalRemoteUsername" class="form-label">Username <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="modalRemoteUsername" placeholder="sa" required>
                                    <div class="form-text">SQL Server authentication username</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="modalRemotePassword" class="form-label">Password <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="modalRemotePassword" placeholder="Enter password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePasswordVisibility">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">SQL Server authentication password</div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="modalConnectionTimeout" class="form-label">Connection Timeout (seconds)</label>
                                    <input type="number" class="form-control" id="modalConnectionTimeout" placeholder="30" value="30" min="5" max="300">
                                    <div class="form-text">Maximum time to wait for connection</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check form-switch mt-4">
                                        <input class="form-check-input" type="checkbox" id="modalFallbackEnabled" checked>
                                        <label class="form-check-label" for="modalFallbackEnabled">
                                            Enable automatic fallback
                                        </label>
                                        <div class="form-text">Automatically switch to available connection if primary fails</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Real-time Validation Status -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div id="modalConfigValidation" class="alert" style="display: none;">
                                        <div id="modalValidationContent"></div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Connection Test Results -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">Connection Status</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        <i class="fas fa-desktop me-2"></i>Local
                                                    </h6>
                                                    <div class="d-flex align-items-center">
                                                        <div id="modalLocalStatusIndicator" class="status-indicator disconnected me-2"></div>
                                                        <span id="modalLocalStatusText" class="fw-bold">Not Tested</span>
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="modalTestLocalBtn">
                                                    <i class="fas fa-flask me-1"></i>Test
                                                </button>
                                            </div>
                                            <small class="text-muted" id="modalLocalLastTest">Ready for testing</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        <i class="fas fa-cloud me-2"></i>Remote
                                                    </h6>
                                                    <div class="d-flex align-items-center">
                                                        <div id="modalRemoteStatusIndicator" class="status-indicator disconnected me-2"></div>
                                                        <span id="modalRemoteStatusText" class="fw-bold">Not Tested</span>
                                                    </div>
                                                </div>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="modalTestRemoteBtn">
                                                    <i class="fas fa-flask me-1"></i>Test
                                                </button>
                                            </div>
                                            <small class="text-muted" id="modalRemoteLastTest">Ready for testing</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Connection History -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">Recent Connection History</h6>
                            <div id="modalConnectionHistory" class="connection-history-container" style="max-height: 200px; overflow-y: auto;">
                                <div class="text-muted text-center py-3">
                                    <i class="fas fa-history me-2"></i>
                                    No connection attempts yet
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-outline-info" id="modalTestAllBtn">
                        <i class="fas fa-search me-1"></i>
                        <span id="modalTestAllLoading" class="spinner-border spinner-border-sm me-1" style="display: none;"></span>
                        Test All Connections
                    </button>
                    <button type="button" class="btn btn-success" id="modalSaveConfigBtn">
                        <i class="fas fa-save me-1"></i>
                        <span id="modalSaveLoading" class="spinner-border spinner-border-sm me-1" style="display: none;"></span>
                        Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='xlsx.full.min.js') }}"></script>
    <script src="{{ url_for('static', filename='simple-db-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='database-connection-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
